/**
 * API 路径管理工具
 *
 * 根据不同环境处理 API 请求路径
 * - 开发环境：使用 /api/v1 前缀
 * - 生产环境：使用 /admin-api/v1 前缀
 * - 本地开发：如果配置了完整的 VITE_API_BASE_URL，则使用该值
 */

// 判断当前环境
const isDevelopment = import.meta.env.DEV;
const baseUrl = import.meta.env.VITE_API_BASE_URL;

// 根据环境确定默认的API前缀
const defaultApiPrefix = isDevelopment ? "/api/v1" : "/admin-api/v1";

console.log("当前环境:", isDevelopment ? "开发环境" : "生产环境");
console.log("默认API前缀:", defaultApiPrefix);
if (baseUrl) {
  console.log("自定义API基础URL:", baseUrl);
}

/**
 * 获取 API 完整路径
 *
 * @param path API 路径，例如 "/manifests/search" 或 "/api/v1/manifests/search"
 * @returns 根据环境处理后的完整路径
 */
export function getApiPath(path: string): string {
  // 如果是绝对路径（包含协议和主机名），则直接返回
  if (path.startsWith("http://") || path.startsWith("https://")) {
    return path;
  }

  // 如果已经配置了完整的基础URL，优先使用
  if (baseUrl) {
    // 处理路径中可能的重复前缀
    if (path.startsWith("/api/") && baseUrl.endsWith("/api")) {
      return `${baseUrl}${path.substring(4)}`;
    }
    if (path.startsWith("/admin-api/") && baseUrl.endsWith("/admin-api")) {
      return `${baseUrl}${path.substring(10)}`;
    }
    if (
      path.startsWith("/v1/") &&
      (baseUrl.endsWith("/api") || baseUrl.endsWith("/admin-api"))
    ) {
      return `${baseUrl}${path}`;
    }

    // 确保路径正确拼接
    return `${baseUrl}${path.startsWith("/") ? path : `/${path}`}`;
  }

  // 如果路径已经以正确的前缀开头，则不做任何修改
  if (path.startsWith("/api/v1/") || path.startsWith("/admin-api/v1/")) {
    return path;
  }

  // 其他所有路径，使用环境对应的默认前缀
  return `${defaultApiPrefix}${path.startsWith("/") ? path : `/${path}`}`;
}

/**
 * 获取 API 完整路径并记录日志
 *
 * @param path API 路径
 * @returns 处理后的完整路径
 */
export function getApiPathWithLog(path: string): string {
  const fullPath = getApiPath(path);
  console.log(`API Path: ${path} -> ${fullPath}`);
  return fullPath;
}
