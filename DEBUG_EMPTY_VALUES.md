# 调试空值问题指南

## 问题描述

现在 `dueDate` 和 `notes` 字段能够传递了，但是应该有值的时候却传递的是空值。

## 调试步骤

### 1. 启动应用并准备调试

```bash
npm run dev
```

访问：http://localhost:5179/#/billing/bills

### 2. 打开开发者工具

- 按 F12 打开开发者工具
- 切换到 **Console** 标签
- 清空控制台日志

### 3. 执行生成账单流程

1. **第一步：选择时间**

   - 选择合适的时间范围
   - 点击"查询用户列表"

2. **第二步：选择用户**

   - 选择一个有数据的用户
   - 点击"选择"按钮
   - **查看控制台输出的表单初始化日志**

3. **第三步：编辑模板**

   - 检查页面上显示的表单字段值
   - 点击"下一步"

4. **第四步：确认生成**
   - 点击"确认生成账单"
   - **查看控制台输出的详细调试信息**

## 关键调试信息

### 第二步的表单初始化日志

期望看到：

```
=== 表单初始化调试 ===
模板数据: { generalTemplate: {...}, batteryTemplate: {...}, ... }
用户信息: { nickname: "用户昵称" }
当前时间: 2024年12月
表单初始化数据: {
  generalTemplate: {...},
  batteryTemplate: {...},
  dueDate: Moment { ... },  // 应该是 dayjs 对象
  notes: "用户昵称 2024年12月 运费账单",  // 应该有具体内容
  currency: "CNY"
}
dueDate初始值: Moment { ... }
notes初始值: "用户昵称 2024年12月 运费账单"
currency初始值: "CNY"

=== 表单设置后验证 ===
实际表单值: { ... }
dueDate实际值: Moment { ... }  // 应该和初始值一致
notes实际值: "用户昵称 2024年12月 运费账单"  // 应该和初始值一致
currency实际值: "CNY"
```

### 第四步的表单获取日志

期望看到：

```
=== 表单数据调试开始 ===
表单原始数据: { dueDate: Moment {...}, notes: "...", currency: "CNY", ... }

=== 基本字段检查 ===
dueDate 字段存在: true
notes 字段存在: true
currency 字段存在: true

=== 付款截止日期调试 ===
付款截止日期 - 原始值: Moment { ... }
付款截止日期 - 类型: object
付款截止日期 - 是否为dayjs对象: true
付款截止日期 - 格式化后: "2024-12-31"

=== 账单备注调试 ===
账单备注 - 原始值: "用户昵称 2024年12月 运费账单"
账单备注 - 类型: string
账单备注 - 长度: 20
账单备注 - trim后: "用户昵称 2024年12月 运费账单"
账单备注 - trim后长度: 20

=== 货币单位调试 ===
货币单位: "CNY"
货币单位 - 类型: string
```

## 可能的问题和解决方案

### 问题 1：表单初始化失败

**症状**：

- `dueDate初始值` 显示为 `undefined` 或其他非 dayjs 对象
- `notes初始值` 显示为 `undefined` 或空字符串

**可能原因**：

- `dayjs()` 函数调用失败
- 用户昵称获取失败
- 表单字段被模板数据覆盖

**检查**：

1. 确认 `user.nickname` 是否有值
2. 确认 `dayjs()` 是否正常工作
3. 确认模板数据中是否包含同名字段

### 问题 2：表单设置后值丢失

**症状**：

- 初始化时有值，但设置后验证时变成了 `undefined`

**可能原因**：

- Ant Design Form 的字段名冲突
- 表单验证规则问题
- React 渲染时序问题

**解决方案**：

- 检查表单字段名是否与模板字段冲突
- 确认表单验证规则配置

### 问题 3：表单获取时值为空

**症状**：

- 设置后验证有值，但在生成账单时获取不到

**可能原因**：

- 表单字段名在嵌套结构中被覆盖
- `getFieldsValue()` 方法使用不当
- 表单状态被其他操作重置

**解决方案**：

- 使用 `getFieldValue(fieldName)` 单独获取特定字段
- 检查是否有其他地方调用了 `resetFields()` 或 `setFieldsValue()`

## 紧急修复方案

如果上述调试发现问题复杂，可以使用以下临时修复方案：

### 方案 1：使用 getFieldValue 单独获取

```typescript
// 替换 getFieldsValue()
const dueDate = templateForm.getFieldValue("dueDate");
const notes = templateForm.getFieldValue("notes");
const currency = templateForm.getFieldValue("currency");

console.log("单独获取字段:");
console.log("dueDate:", dueDate);
console.log("notes:", notes);
console.log("currency:", currency);
```

### 方案 2：强制设置默认值

```typescript
// 如果表单值为空，使用强制默认值
const dueDateFormatted =
  formValues.dueDate?.format("YYYY-MM-DD") ||
  dayjs().add(30, "day").format("YYYY-MM-DD");
const notesText =
  formValues.notes?.trim() ||
  `${selectedUser.nickname} ${dayjs().format("YYYY年MM月")} 运费账单`;
```

### 方案 3：直接使用状态值

```typescript
// 不依赖表单，直接使用组件状态
const dueDateFormatted = dayjs().add(30, "day").format("YYYY-MM-DD");
const notesText = `${selectedUser.nickname} ${dayjs().format(
  "YYYY年MM月"
)} 运费账单`;
```

## 下一步行动

1. **执行完整的调试流程**，记录所有控制台输出
2. **截图保存关键的调试信息**
3. **根据调试结果确定具体问题点**
4. **选择合适的修复方案**

如果调试信息显示异常，请将完整的控制台输出发送给开发者进行进一步分析。
