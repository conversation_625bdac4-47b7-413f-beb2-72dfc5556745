// import { useState } from 'react' // 移除默认的 useState
// import reactLogo from './assets/react.svg' // 移除默认 Logo
// import viteLogo from '/vite.svg' // 移除默认 Logo
// import './App.css' // 移除默认样式
import { useState, useEffect } from "react";
import { HashRouter, Routes, Route, Navigate } from "react-router-dom";
import { ConfigProvider, App as AntdApp } from "antd";
import zhCN from "antd/locale/zh_CN";
import LoginPage from "./pages/LoginPage.tsx";
import MainLayout from "./layouts/MainLayout.tsx";
import Dashboard from "./pages/Dashboard/Dashboard.tsx";
import ShipmentTracking from "./pages/ShipmentTracking/ShipmentTracking.tsx";
import WarehouseManagement from "./pages/WarehouseManagement/WarehouseManagement.tsx";
import CustomerManagement from "./pages/CustomerManagement/CustomerManagement.tsx";
import BillingFinance from "./pages/BillingFinance/BillingFinance.tsx";
import FinancialAdjustmentsPage from "./pages/BillingFinance/FinancialAdjustmentsPage";
import BillManagementPage from "./pages/BillingFinance/BillManagementPage";
import BillDetailPage from "./pages/BillingFinance/BillDetailPage";
import BillingCycleManagementPage from "./pages/BillingFinance/BillingCycleManagementPage";
import ReportsAnalytics from "./pages/ReportsAnalytics/ReportsAnalytics.tsx";
import UserManagement from "./pages/SystemSettings/UserManagement.tsx";
import RoleManagement from "./pages/SystemSettings/RoleManagement.tsx";
import OrderForecast from "./pages/OrderForecast/OrderForecast.tsx";
import ForecastAuditPage from "./pages/ForecastManagement/ForecastAuditPage";
import ProblemTicketPage from "./pages/OrderManagement/ProblemTicketPage";
import ManifestListPage from "./pages/OrderManagement/ManifestListPage";
import ShippingFeeTemplatesPage from "./pages/ShippingFeeManagement/ShippingFeeTemplatesPage";
import UserTemplateConfigPage from "./pages/ShippingFeeManagement/UserTemplateConfigPage";
// import NotFoundPage from './pages/NotFoundPage'; // 如果需要可以取消注释并创建 404 页面
import { PendingCountProvider } from "./contexts/PendingCountContext"; // 导入 Provider
import SessionExpiredModal from "./components/auth/SessionExpiredModal.tsx"; // 导入弹窗组件
import eventBus from "./utils/eventBus"; // 导入事件总线
import { setGlobalMessageInstance } from "./services/apiClient";

// 辅助函数：检查认证状态
const checkAuth = (): boolean => {
  return !!localStorage.getItem("authToken");
};

// 内部App组件，用于使用useApp hook
const AppContent = () => {
  const { message } = AntdApp.useApp();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(checkAuth());
  const [sessionExpiredModalOpen, setSessionExpiredModalOpen] =
    useState<boolean>(false);

  // 设置全局message实例
  useEffect(() => {
    setGlobalMessageInstance(message);
    console.log("✅ 全局message实例已设置");
  }, [message]);

  // 处理登录逻辑
  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  // 处理登出逻辑
  const handleLogout = () => {
    localStorage.removeItem("authToken");
    setIsAuthenticated(false);
  };

  // 监听会话过期事件
  useEffect(() => {
    const handleSessionExpired = () => {
      console.log("会话已过期，显示弹窗");
      setSessionExpiredModalOpen(true);
    };

    eventBus.on("sessionExpired", handleSessionExpired);

    return () => {
      eventBus.off("sessionExpired", handleSessionExpired);
    };
  }, []);

  // 添加依赖，确保函数在状态更新后能正确引用

  const closeSessionExpiredModal = () => {
    setSessionExpiredModalOpen(false);
    // 导航到登录页的逻辑在 SessionExpiredModal 组件内部处理
  };

  // 处理会话过期时的登出
  const handleSessionExpiredLogout = () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem("userInfo"); // 如果有用户信息也清除
    setIsAuthenticated(false);
    setSessionExpiredModalOpen(false);
  };

  return (
    <HashRouter>
      <PendingCountProvider>
        {" "}
        {/* 在这里包裹 */}
        <Routes>
          {/* 登录页路由 */}
          <Route
            path="/login"
            element={
              !isAuthenticated ? ( // 简化条件：未认证则显示登录页
                <LoginPage onLogin={handleLogin} />
              ) : (
                <Navigate to="/dashboard" replace />
              )
            }
          />

          {/* 主应用路由 - 使用 MainLayout 包裹 */}
          <Route
            path="/"
            element={
              isAuthenticated || sessionExpiredModalOpen ? ( // 修改条件：已认证或弹窗打开时显示主布局
                <MainLayout onLogout={handleLogout} />
              ) : (
                <Navigate to="/login" replace />
              )
            }
          >
            {/* 默认子路由，重定向到仪表盘 */}
            <Route index element={<Navigate to="dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="orders">
              {/* 添加订单列表路由 */}
              <Route index element={<Navigate to="list" replace />} />
              <Route path="list" element={<ManifestListPage />} />
              <Route path="forecast" element={<OrderForecast />} />
              <Route path="problem-tickets" element={<ProblemTicketPage />} />
            </Route>
            <Route path="forecast">
              <Route index element={<Navigate to="audit" replace />} />
              <Route path="audit" element={<ForecastAuditPage />} />
            </Route>
            <Route path="tracking" element={<ShipmentTracking />} />
            <Route path="warehouse" element={<WarehouseManagement />} />
            <Route path="shipping-fee">
              <Route index element={<Navigate to="templates" replace />} />
              <Route path="templates" element={<ShippingFeeTemplatesPage />} />
              <Route path="user-config" element={<UserTemplateConfigPage />} />
            </Route>
            <Route path="customers" element={<CustomerManagement />} />
            <Route path="billing-finance">
              <Route index element={<BillingFinance />} />
              <Route
                path="adjustments"
                element={<FinancialAdjustmentsPage />}
              />
              <Route path="cycles" element={<BillingCycleManagementPage />} />
              <Route path="bills" element={<BillManagementPage />} />
              <Route path="bills/:billId" element={<BillDetailPage />} />
              <Route
                path="cycles/:cycleId/bills"
                element={<BillManagementPage />}
              />
              <Route
                path="cycles/:cycleId/bills/:billId"
                element={<BillDetailPage />}
              />
            </Route>
            <Route path="reports" element={<ReportsAnalytics />} />
            <Route path="settings">
              <Route path="users" element={<UserManagement />} />
              <Route path="roles" element={<RoleManagement />} />
            </Route>
          </Route>
        </Routes>
        {/* 会话过期弹窗 */}
        <SessionExpiredModal
          isOpen={sessionExpiredModalOpen}
          onClose={closeSessionExpiredModal}
          onSessionExpiredLogout={handleSessionExpiredLogout}
        />
      </PendingCountProvider>
    </HashRouter>
  );
};

// 外层App组件，提供ConfigProvider和AntdApp
function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <AppContent />
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
