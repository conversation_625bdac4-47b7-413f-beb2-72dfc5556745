import { apiClient, ApiResponse } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 运单状态枚举
export enum ManifestStatus {
  PENDING_AUDIT = 0, // 待审核
  FORECASTED = 1, // 已预报
  PICKED_UP = 2, // 已揽件
  SHIPPED = 3, // 已发货
  DELIVERED = 4, // 已送达
}

// 运单状态标签映射
export const MANIFEST_STATUS_MAP = {
  [ManifestStatus.PENDING_AUDIT]: { label: "待审核", color: "default" },
  [ManifestStatus.FORECASTED]: { label: "已预报", color: "processing" },
  [ManifestStatus.PICKED_UP]: { label: "已揽件", color: "warning" },
  [ManifestStatus.SHIPPED]: { label: "已发货", color: "success" },
  [ManifestStatus.DELIVERED]: { label: "已送达", color: "purple" },
};

// 物品类型接口
export interface ManifestItem {
  weight: number;
  quantity: number;
  price: number;
  name: string;
  value: number;
}

// 运单接口
export interface Manifest {
  id: number;
  expressNumber: string;
  orderNumber: string;
  orderNo: string;
  transferredTrackingNumber: string;
  receiverZipCode: string;
  receiverName: string;
  receiverAddress: string;
  receiverPhone: string;
  items: ManifestItem[];
  createTime: string;
  pickUpTime: string;
  shipmentTime: string;
  deliveredTime: string;
  updateTime: string;
  status: number;
  userId: number;
  userNickname: string;
  preRegistrationBatchId: string;
  masterBillId: number;
  remark: string;
  weight: number;
  totalValue: number;
}

// 查询参数接口
export interface ManifestQueryParams {
  query?: string; // 单号搜索(运单号、转单号、系统订单号、商家订单号)
  createTimeStart?: string; // 创建开始时间
  createTimeEnd?: string; // 创建结束时间
  pickUpTimeStart?: string; // 揽件开始时间
  pickUpTimeEnd?: string; // 揽件结束时间
  shipmentTimeStart?: string; // 发货开始时间
  shipmentTimeEnd?: string; // 发货结束时间
  deliveredTimeStart?: string; // 签收开始时间
  deliveredTimeEnd?: string; // 签收结束时间
  status?: number; // 运单状态
  userId?: number; // 用户ID
  masterBillId?: number; // 提单ID
  page?: number; // 页码
  pageSize?: number; // 每页数量
}

// 查询结果接口
export interface ManifestQueryResult {
  total: number;
  list: Manifest[];
}

// 根据快递单号模糊查询运单接口参数
interface SearchManifestsByExpressNumberParams {
  expressNumber: string;
  page?: number;
  pageSize?: number;
}

// 运单搜索结果项接口
export interface ManifestSearchItem {
  id: number;
  expressNumber: string;
  orderNumber: string;
  orderNo: string;
  transferredTrackingNumber: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  receiverZipCode: string;
  createTime: string;
  status: number;
  userId: number;
  userNickname: string;
  items: ManifestItem[];
}

// 运单搜索响应接口
interface SearchManifestsResponse {
  success: boolean;
  data: {
    total: number;
    list: ManifestSearchItem[];
  };
}

// 创建模拟数据
const getMockManifests = (count: number = 10): Manifest[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    expressNumber: `EXP${1000 + index}`,
    orderNumber: `ORD${2000 + index}`,
    orderNo: `SO${3000 + index}`,
    transferredTrackingNumber: `TRF${4000 + index}`,
    receiverZipCode: `10000${index}`,
    receiverName: `收件人${index + 1}`,
    receiverAddress: `北京市朝阳区测试地址${index + 1}号`,
    receiverPhone: `1380000${1000 + index}`,
    items: [
      {
        name: `测试物品${index + 1}`,
        quantity: Math.floor(Math.random() * 5) + 1,
        weight: parseFloat((Math.random() * 2 + 0.5).toFixed(2)),
        price: parseFloat((Math.random() * 100 + 10).toFixed(2)),
        value: parseFloat((Math.random() * 500 + 50).toFixed(2)),
      },
    ],
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    pickUpTime: new Date(Date.now() - Math.random() * 25 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    shipmentTime: new Date(
      Date.now() - Math.random() * 20 * 24 * 60 * 60 * 1000
    )
      .toISOString()
      .split("T")[0],
    deliveredTime: new Date(
      Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000
    )
      .toISOString()
      .split("T")[0],
    updateTime: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    status: Math.floor(Math.random() * 5),
    userId: Math.floor(Math.random() * 10) + 1,
    userNickname: `用户${Math.floor(Math.random() * 10) + 1}`,
    preRegistrationBatchId: `BATCH${5000 + index}`,
    masterBillId: Math.floor(Math.random() * 5) + 1,
    remark: `备注信息${index + 1}`,
    weight: parseFloat((Math.random() * 10 + 1).toFixed(2)),
    totalValue: parseFloat((Math.random() * 1000 + 100).toFixed(2)),
  }));
};

/**
 * 搜索运单列表
 * @param params 查询参数
 * @returns 运单列表数据
 */
export const searchManifests = async (
  params: ManifestQueryParams
): Promise<ManifestQueryResult> => {
  try {
    const response = await apiClient.get<ApiResponse<ManifestQueryResult>>(
      getApiPath("/manifests/search"),
      { params }
    );
    return response.data.data || { total: 0, list: [] };
  } catch (error) {
    console.error("获取运单列表失败:", error);
    // 返回模拟数据
    const mockData = getMockManifests(20);
    const filteredData =
      params.status !== undefined
        ? mockData.filter((item) => item.status === params.status)
        : mockData;

    return {
      total: filteredData.length,
      list: filteredData.slice(
        ((params.page || 1) - 1) * (params.pageSize || 10),
        (params.page || 1) * (params.pageSize || 10)
      ),
    };
  }
};

/**
 * 获取运单详情
 * @param id 运单ID
 * @returns 运单详情数据
 */
export const getManifestById = async (id: number): Promise<Manifest | null> => {
  try {
    const response = await apiClient.get<ApiResponse<Manifest>>(
      getApiPath(`/manifests/${id}`)
    );
    return response.data.data;
  } catch (error) {
    console.error("获取运单详情失败:", error);
    // 返回模拟数据
    const mockManifests = getMockManifests(50);
    return mockManifests.find((item) => item.id === id) || null;
  }
};

/**
 * 导出运单数据
 * @param params 查询参数，与搜索参数相同
 * @returns Blob数据用于下载
 */
export const exportManifests = async (
  params: ManifestQueryParams
): Promise<Blob> => {
  try {
    const response = await apiClient.get(getApiPath("/manifests/export"), {
      params,
      responseType: "blob", // 确保返回Blob数据
    });
    return response.data;
  } catch (error) {
    console.error("导出运单失败:", error);
    throw error;
  }
};

/**
 * 根据快递单号模糊查询运单
 */
export const searchManifestsByExpressNumber = async (
  params: SearchManifestsByExpressNumberParams
): Promise<ManifestSearchItem[]> => {
  try {
    const response = await apiClient.get<SearchManifestsResponse>(
      getApiPath("/manifests/by-express-number"),
      { params }
    );

    if (response.data.success) {
      return response.data.data.list;
    } else {
      throw new Error("搜索运单失败");
    }
  } catch (error) {
    console.error("搜索运单失败:", error);
    throw error;
  }
};
