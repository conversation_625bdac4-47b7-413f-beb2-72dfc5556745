# 运费模板修改功能测试指南

## 问题描述

用户报告：运费模板配置修改后，后端接收到的还是原来的值，没有传递修改后的数据。

## 修复方案

我们采用了多重获取策略来确保能够正确获取用户修改后的模板参数：

### 1. 四重后备获取策略

对于每个模板字段，我们按以下优先级获取：

```typescript
const firstWeightPrice =
  templateForm.getFieldValue(["generalTemplate", "firstWeightPrice"]) ?? // 1. 嵌套路径获取
  generalTemplateSingle?.firstWeightPrice ?? // 2. 单独模板获取
  formValues.generalTemplate?.firstWeightPrice ?? // 3. 批量表单获取
  templatesData.generalTemplate.firstWeightPrice; // 4. 原始模板数据
```

### 2. 详细调试信息

添加了完整的调试输出来追踪数据流：

- 两种获取方式对比
- 逐个字段获取测试
- 模板构建详情
- 字段变更对比

## 测试步骤

### 1. 访问生成账单功能

访问：http://localhost:5191/#/billing/bills （注意端口已变更）

### 2. 完成前两步选择

1. **选择时间范围** - 选择有数据的时间段
2. **选择用户** - 选择有运费模板的用户

### 3. 关键测试：修改模板参数

在第三步"配置模板"页面，测试修改以下参数：

#### 测试场景 1：修改普通货物模板

**原始值示例**：

- 首重价格：22.00
- 首重范围：0.5
- 续重价格：6.00
- 续重区间：0.5
- 轻抛系数：5000
- 三边和阈值：120.0

**修改测试**：

- **首重价格**：22.00 → **25.00**
- **续重价格**：6.00 → **8.00**
- **轻抛系数**：5000 → **6000**

#### 测试场景 2：修改带电货物模板

如果用户有带电货物模板，同样修改关键参数：

- **首重价格**：原值 → **新值**
- **续重价格**：原值 → **新值**

#### 测试场景 3：修改投函货物模板

如果用户有投函货物模板，同样修改关键参数。

### 4. 验证确认页面显示

**点击"下一步"进入确认页面**

在确认页面，验证以下内容：

#### A. 视觉验证

1. **变更标识**：修改过的模板会显示橙色"已修改"标签
2. **高亮显示**：修改后的字段值会用红色粗体显示
3. **原值对比**：修改的字段会在后面显示"(原: 原值)"

例如：

- 首重价格：**25** 元 (原: 22) ← 红色粗体显示修改后的值
- 续重价格：**8** 元 (原: 6) ← 红色粗体显示修改后的值
- 轻抛系数：**6000** (原: 5000) ← 红色粗体显示修改后的值

#### B. 调试信息验证

1. **打开开发者工具**（F12）
2. **切换到 Console 标签**
3. 查看"确认页面模板显示调试"信息

```console
=== 确认页面模板显示调试 ===
模板类型: 普通货物模板
原始值: {firstWeightPrice: 22, continuedWeightPrice: 6, bulkCoefficient: 5000}
显示值: {firstWeightPrice: 25, continuedWeightPrice: 8, bulkCoefficient: 6000}
```

### 5. 测试账单生成

**点击"确认生成账单"**

### 6. 验证账单生成调试输出

查看以下关键调试信息：

#### A. 两种获取方式对比

```console
=== 两种模板获取方式对比 ===
getFieldsValue方式:
  generalTemplate: {firstWeightPrice: 25, continuedWeightPrice: 8, ...}
  batteryTemplate: {...}
  postBoxTemplate: {...}
getFieldValue方式:
  generalTemplate: {firstWeightPrice: 25, continuedWeightPrice: 8, ...}
  batteryTemplate: {...}
  postBoxTemplate: {...}
```

#### B. 逐个字段获取测试

```console
=== 更细粒度的字段获取测试 ===
普通货物模板 - 逐个字段获取: {
  firstWeightPrice: 25,      // ✅ 应该是修改后的值
  firstWeightRange: 0.5,
  continuedWeightPrice: 8,   // ✅ 应该是修改后的值
  continuedWeightInterval: 0.5,
  bulkCoefficient: 6000,     // ✅ 应该是修改后的值
  threeSidesStart: 120
}
```

#### C. 模板构建详情

```console
=== 普通货物模板构建详情 ===
原始模板数据: {firstWeightPrice: 22, continuedWeightPrice: 6, bulkCoefficient: 5000, ...}
最终模板数据: {firstWeightPrice: 25, continuedWeightPrice: 8, bulkCoefficient: 6000, ...}
字段对比:
  首重价格: 原始 22 → 最终 25     // ✅ 应该显示变更
  首重范围: 原始 0.5 → 最终 0.5
  续重价格: 原始 6 → 最终 8       // ✅ 应该显示变更
```

### 7. 验证 Network 请求

在 **Network** 标签中找到生成账单的请求，检查参数：

```json
{
  "startTime": "2025-05-01 00:00:00",
  "endTime": "2025-05-27 00:00:00",
  "userId": 50,
  "currency": "CNY",
  "dueDate": "2025-06-26",
  "notes": "用户备注",
  "generalTemplate": {
    "id": 123,
    "name": "普通货物模板",
    "type": 1,
    "firstWeightPrice": 25,          // ✅ 修改后的值
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 8,       // ✅ 修改后的值
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 6000,         // ✅ 修改后的值
    "threeSidesStart": 120
  },
  "batteryTemplate": {...},
  "postBoxTemplate": {...}
}
```

## 问题诊断

### 情况 1：确认页面显示回退到原值（已修复）

**问题现象**：在模板配置页面修改了参数，点击"下一步"进入确认页面时，显示的值又变回了原值。

**原因**：确认页面使用的是简单的 `||` 逻辑回退，当表单字段为 `undefined` 时会回退到原始值。

**修复方案**：

- 使用和生成账单时相同的四重后备获取策略
- 添加视觉标识来区分修改和原值
- 增加调试信息来追踪显示值的获取过程

**验证修复**：

- ✅ 确认页面正确显示修改后的值
- ✅ 修改的字段用红色粗体高亮显示
- ✅ 显示"(原: 原值)"对比信息
- ✅ 模板标题显示"已修改"标签

### 情况 2：调试信息显示正确，但 Network 请求错误

**原因**：模板数据传递到后端时可能有序列化问题

**解决**：检查 `generateBilling` 函数的参数处理

### 情况 3：逐个字段获取为空或错误

**原因**：表单字段名称或嵌套结构配置错误

**调试步骤**：

1. 检查 `renderTemplateForm` 中的字段名称
2. 确认 `name={[templateKey, "firstWeightPrice"]}` 的配置
3. 验证表单初始化是否正确

### 情况 4：两种获取方式都显示原始值

**原因**：表单值根本没有更新，可能是：

- 表单控件的 `onChange` 没有触发
- 表单字段绑定错误
- 表单验证阻止了值的更新

**解决方案**：

1. 检查 InputNumber 组件的配置
2. 确认表单验证规则
3. 手动触发表单更新事件

### 情况 5：某些字段能获取，某些不能

**原因**：字段配置不一致

**检查点**：

- 所有字段的 `name` 属性配置
- `rules` 验证规则设置
- 字段类型和 InputNumber 配置

## 成功标准

✅ **修复成功的标志**：

1. **调试信息正确**：

   - 逐个字段获取显示修改后的值
   - 模板构建详情显示正确的变更对比
   - 最终参数包含用户修改的值

2. **Network 请求正确**：

   - 请求参数中的模板数据是修改后的值
   - 所有修改的字段都正确传递

3. **后端接收正确**：
   - 后端能够接收到修改后的模板参数
   - 生成的账单使用了新的模板配置

## 常见问题

### Q1：为什么需要四重后备策略？

A：Ant Design 的嵌套表单在某些情况下，不同的获取方式可能返回不同的结果：

- `getFieldsValue()` 可能受到表单结构影响
- `getFieldValue("template")` 可能返回整个对象
- `getFieldValue(["template", "field"])` 直接获取嵌套字段
- 原始数据作为最后的后备

### Q2：为什么添加这么多调试信息？

A：因为表单嵌套获取的问题比较复杂，需要详细的调试信息来：

- 对比不同获取方式的结果
- 追踪数据在哪一步丢失
- 确认最终传递给后端的参数

### Q3：性能会受影响吗？

A：调试信息只在开发环境中有用，生产环境可以通过构建工具去除 `console.log`。
多重获取的性能影响很小，因为只在生成账单时执行一次。

## 验证清单

在测试时，请确认以下各项：

### 模板配置页面

- [ ] 可以正常修改模板字段的值
- [ ] 表单验证正常工作
- [ ] 点击"下一步"能正常进入确认页面

### 确认页面显示

- [ ] 修改过的模板显示橙色"已修改"标签
- [ ] 修改后的字段值用红色粗体显示
- [ ] 修改的字段显示"(原: 原值)"对比信息
- [ ] 未修改的字段正常显示原值
- [ ] 确认页面调试信息显示正确的值

### 账单生成

- [ ] 修改普通货物模板的首重价格，调试信息显示变更
- [ ] 修改续重价格，调试信息显示变更
- [ ] 修改轻抛系数，调试信息显示变更
- [ ] Network 请求中包含修改后的值
- [ ] 如果有多个模板，每个模板的修改都能正确获取
- [ ] 没有修改的字段保持原值不变
- [ ] 后端成功接收并处理修改后的参数

### 完整流程

- [ ] 确认页面显示的值与传递给后端的值一致
- [ ] 生成的账单使用了修改后的模板配置

如果遇到问题，请提供完整的 Console 输出和 Network 请求截图。
