.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.userSelectionCard {
  height: fit-content;
  margin-bottom: 16px;
}

.selectedUserCard {
  margin-top: 16px;
  border: 1px solid #1890ff;
  background-color: #f6ffed;
}

.configCard {
  min-height: 600px;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.typeCard {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.typeCard:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

/* 用户列表样式 */
.userList {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
}

.userListItem {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px 16px !important;
  border-bottom: 1px solid #f0f0f0;
}

.userListItem:hover {
  background-color: #f5f5f5;
}

.userListItem:last-child {
  border-bottom: none;
}

.selectedUserItem {
  background-color: #e6f7ff !important;
  border-color: #91d5ff;
}

.selectedUserItem:hover {
  background-color: #bae7ff !important;
}

/* 模板选项样式 */
.templateOption {
  padding: 4px 0;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
}

.templateName {
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.templateDetails {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .configCard {
    min-height: auto;
  }

  .userList {
    max-height: 300px;
  }
}

/* 未保存更改提示样式 */
.unsavedChangesAlert {
  margin-bottom: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}
