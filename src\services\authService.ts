import { AxiosError, AxiosResponse } from "axios";
import { apiClient, rawApiClient, ApiResponse } from "./apiClient";
import { getApiPath } from "./apiPaths";

// --- Authentication Service Specific Types ---
export interface LoginPayload {
  username?: string;
  password?: string;
}

export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  [key: string]: string | number | null | undefined; // Allow other fields
}

export interface LoginResponseData {
  token: string;
  userInfo: UserInfo;
}

// --- Authentication API Functions ---

/**
 * Handles user login using the main apiClient.
 * apiClient's interceptors will show global messages for business errors and throw an Error.
 * This function catches such errors and returns a standardized ApiResponse.
 */
export const loginApi = async (
  credentials: LoginPayload
): Promise<ApiResponse<LoginResponseData>> => {
  try {
    // apiClient.post returns AxiosResponse<ApiResponse<LoginResponseData>>
    // The interceptor for apiClient already checks for business errors from response.data and throws if any.
    const response = await apiClient.post<ApiResponse<LoginResponseData>>(
      getApiPath("/login"),
      credentials
    );
    // If no error was thrown by interceptor, it implies business success.
    return response.data; // Return the data part of the response
  } catch (error) {
    // This catches errors thrown by apiClient's interceptors (typically generic Error instances)
    // or network errors if they bypass interceptor logic somehow.
    console.error("Login API error (loginApi):", error);
    const errorCode = 100001; // Default unknown error
    let errorMessage = "登录时发生未知错误";

    if (error instanceof Error) {
      errorMessage = error.message;
      // Further parsing of error.message could be done here if needed to extract more details
    }
    // Note: If error is an AxiosError, it might mean the request didn't even reach the point
    // where the response interceptor could process it, or it's an error from rawApiClient usage if misused.
    // However, loginApi is designed to use apiClient, whose interceptors should convert AxiosErrors to generic Errors.

    return {
      success: false,
      errorCode,
      errorMessage,
      requestId: "", // Typically not available for client-side errors or interceptor-originated errors
      timestamp: new Date().toISOString(),
      data: null,
    };
  }
};

/**
 * Handles user login using rawApiClient for custom error handling.
 * Returns the backend's ApiResponse structure directly or a constructed error ApiResponse.
 */
export const loginApiRaw = async (
  credentials: LoginPayload
): Promise<ApiResponse<LoginResponseData>> => {
  try {
    const response: AxiosResponse<ApiResponse<LoginResponseData>> =
      await rawApiClient.post(getApiPath("/login"), credentials);
    return response.data; // Return the data part of the response (which is ApiResponse<LoginResponseData>)
  } catch (error) {
    const axiosError = error as AxiosError<ApiResponse<LoginResponseData>>;
    let errorResponse: ApiResponse<LoginResponseData>;

    if (axiosError.response) {
      // The server responded with a status code outside the 2xx range
      if (
        axiosError.response.data &&
        typeof axiosError.response.data.success === "boolean"
      ) {
        // Backend responded with our standard API error structure
        errorResponse = axiosError.response.data;
      } else {
        // Backend responded with an error, but not in our standard API structure
        errorResponse = {
          success: false,
          errorCode: axiosError.response.status, // Use HTTP status code
          errorMessage:
            (axiosError.response.data as any)?.message ||
            axiosError.message ||
            "登录请求失败",
          requestId: (axiosError.response.data as any)?.requestId || "",
          timestamp:
            (axiosError.response.data as any)?.timestamp ||
            new Date().toISOString(),
          data: null,
        };
      }
    } else if (axiosError.request) {
      // The request was made but no response was received
      errorResponse = {
        success: false,
        errorCode: 100009, // Custom code for network error (ERROR_SERVICE_UNAVAILABLE)
        errorMessage: "网络错误，无法连接到服务器",
        requestId: "",
        timestamp: new Date().toISOString(),
        data: null,
      };
    } else {
      // Something happened in setting up the request that triggered an Error
      errorResponse = {
        success: false,
        errorCode: 100001, // Custom code for unknown client-side error (ERROR_UNKNOWN)
        errorMessage: `登录请求处理失败: ${axiosError.message}`,
        requestId: "",
        timestamp: new Date().toISOString(),
        data: null,
      };
    }
    return errorResponse;
  }
};
