import React, { <PERSON> } from "react";
import { But<PERSON>, Dropdown, Typo<PERSON>, Space } from "antd";
import {
  DownOutlined,
  PrinterOutlined,
  ExportOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import type { MenuProps } from "antd";

interface BatchActionsProps {
  selectedCount: number;
  onBatchPrint?: () => void;
  onBatchExport?: () => void;
  onBatchAssignToMasterBill?: () => void;
  onBatchDelete?: () => void;
}

const BatchActions: FC<BatchActionsProps> = ({
  selectedCount,
  onBatchPrint,
  onBatchExport,
  onBatchAssignToMasterBill,
  onBatchDelete,
}) => {
  const isDisabled = selectedCount === 0;

  const menuItems: MenuProps["items"] = [
    {
      key: "print",
      label: "批量打印面单",
      icon: <PrinterOutlined />,
      disabled: isDisabled || !onBatchPrint,
      onClick: onBatchPrint,
    },
    {
      key: "export",
      label: "导出选中运单",
      icon: <ExportOutlined />,
      disabled: isDisabled || !onBatchExport,
      onClick: onBatchExport,
    },
    {
      key: "assignToMasterBill",
      label: "分配到提单",
      disabled: isDisabled || !onBatchAssignToMasterBill,
      onClick: onBatchAssignToMasterBill,
    },
    {
      key: "divider",
      type: "divider",
    },
    {
      key: "delete",
      label: "批量删除",
      icon: <DeleteOutlined />,
      danger: true,
      disabled: isDisabled || !onBatchDelete,
      onClick: onBatchDelete,
    },
  ];

  return (
    <Space>
      {selectedCount > 0 && (
        <Typography.Text strong>已选择 {selectedCount} 条记录</Typography.Text>
      )}

      <Dropdown menu={{ items: menuItems }} disabled={isDisabled}>
        <Button>
          批量操作 <DownOutlined />
        </Button>
      </Dropdown>
    </Space>
  );
};

export default BatchActions;
