import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Card,
  Statistic,
  Button,
  List,
  Typography,
  Tag,
  Space,
  Progress,
  Avatar,
  message,
} from "antd";
import {
  ArrowUpOutlined,
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  UserOutlined,
  AuditOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import styles from "./Dashboard.module.css";
import { useNavigate } from "react-router-dom";
import { useSharedPendingCount } from "../../contexts/PendingCountContext";
import { getProblemManifestCount } from "../../services/dashboardService";
import { ApiError } from "../../services/apiClient";
// 引入图表库（如果需要真实图表）或使用占位符
// import { Pie, Line } from '@ant-design/charts'; // 示例

const { Title, Text, Paragraph } = Typography;

// 为模拟数据添加类型
interface TodoItem {
  id: string;
  title: string;
  status: string;
  priority: "high" | "medium" | "low";
}

interface ActivityItem {
  id: string;
  user: string;
  action: string;
  time: string;
}

// 模拟待办事项数据
const todoData: TodoItem[] = [
  {
    id: "1",
    title: "订单 #ORD12345 待审核",
    status: "pending",
    priority: "high",
  },
  {
    id: "2",
    title: "运单 #SHP67890 需要补充清关文件",
    status: "action-required",
    priority: "medium",
  },
  {
    id: "3",
    title: "货物 #WHS001 即将超过免费仓储期 (3天)",
    status: "warning",
    priority: "low",
  },
  {
    id: "4",
    title: "客户 [ABC公司] 的合同即将到期",
    status: "info",
    priority: "medium",
  },
];

// 模拟最新动态数据
const activityData: ActivityItem[] = [
  {
    id: "act1",
    user: "管理员",
    action: "创建了新订单 #ORD54321",
    time: "5分钟前",
  },
  {
    id: "act2",
    user: "操作员A",
    action: '更新了运单 #SHP98765 的状态为"运输中"',
    time: "1小时前",
  },
  {
    id: "act3",
    user: "系统",
    action: "检测到运单 #SHP11223 可能延误",
    time: "3小时前",
  },
  { id: "act4", user: "财务B", action: "生成了结算单 #BILL0088", time: "昨天" },
];

// 模拟图表数据 (如果使用真实图表库，数据结构可能不同)
// const pieChartData = [...] // 保持不变
// const lineChartData = [...] // 保持不变

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { pendingCount, loading: pendingCountLoading } =
    useSharedPendingCount();

  // 新增状态：问题订单数量及其加载状态
  const [problemOrderCount, setProblemOrderCount] = useState<number | null>(
    null
  );
  const [problemOrderLoading, setProblemOrderLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchProblemOrderCount = async () => {
      try {
        setProblemOrderLoading(true);
        const count = await getProblemManifestCount();
        setProblemOrderCount(count);
      } catch (error) {
        console.error("获取问题订单数量失败:", error);
        if (error instanceof ApiError) {
          message.error(error.message || "获取问题订单数量失败 (API Error)");
        } else if (error instanceof Error) {
          message.error(error.message || "获取问题订单数量失败 (Error)");
        } else {
          message.error("获取问题订单数量失败，请稍后重试 (Unknown Error)");
        }
        setProblemOrderCount(null); // 出错时可以设置为 null 或 0
      } finally {
        setProblemOrderLoading(false);
      }
    };

    fetchProblemOrderCount();
  }, []);

  const handleNavigateToAudit = () => {
    navigate("/forecast/audit");
  };

  const handleNavigateToProblemTickets = () => {
    navigate("/orders/problem-tickets"); // 导航到问题订单页面
  };

  // 定义为函数式组件
  // 简单的饼图/环形图占位符
  const PieChartPlaceholder = () => (
    <div className={styles.chartPlaceholder}>
      <Title level={5} style={{ textAlign: "center", marginBottom: "20px" }}>
        运单状态分布
      </Title>
      <Progress
        type="circle"
        percent={45}
        format={() => "运输中"}
        style={{ marginRight: 15 }}
      />
      <Progress
        type="circle"
        percent={18}
        format={() => "清关中"}
        status="exception"
        style={{ marginRight: 15 }}
      />
      <Progress
        type="circle"
        percent={80}
        format={() => "已签收"}
        status="success"
      />
      {/* 可以用更复杂的 SVG 或 Canvas 实现 */}
      <Paragraph
        type="secondary"
        style={{ marginTop: 20, textAlign: "center" }}
      >
        此处为图表示例
      </Paragraph>
    </div>
  );

  // 简单的折线图占位符
  const LineChartPlaceholder = () => (
    <div className={styles.chartPlaceholder}>
      <Title level={5} style={{ textAlign: "center", marginBottom: "20px" }}>
        近期订单量趋势
      </Title>
      {/* 绘制简单的折线 */}
      <svg
        width="100%"
        height="150"
        viewBox="0 0 300 150"
        preserveAspectRatio="none"
      >
        <polyline
          points="0,130 50,100 100,110 150,80 200,70 250,90 300,50"
          fill="none"
          stroke="#1890ff"
          strokeWidth="2"
        />
        <line
          x1="0"
          y1="140"
          x2="300"
          y2="140"
          stroke="#f0f0f0"
          strokeWidth="1"
        />
        <line x1="0" y1="0" x2="0" y2="140" stroke="#f0f0f0" strokeWidth="1" />
      </svg>
      <Paragraph
        type="secondary"
        style={{ marginTop: 10, textAlign: "center" }}
      >
        此处为图表示例
      </Paragraph>
    </div>
  );

  return (
    <div className={styles.dashboardContainer}>
      {/* 1. 概览统计卡片 */}
      <Row gutter={[16, 16]} className={styles.summaryRow}>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="今日新增订单"
              value={112}
              precision={0}
              valueStyle={{ color: "#3f8600" }}
              prefix={<ArrowUpOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              较昨日 +12%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Card
            bordered={false}
            hoverable
            onClick={handleNavigateToAudit}
            className={styles.clickableCard}
          >
            <Statistic
              title="待审核预报"
              value={pendingCountLoading ? "-" : pendingCount}
              valueStyle={{ color: pendingCount > 0 ? "#faad14" : undefined }}
              prefix={<AuditOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {pendingCount > 0 ? "点击处理" : "暂无待审核"}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Card
            bordered={false}
            hoverable
            onClick={handleNavigateToProblemTickets}
            className={styles.clickableCard}
          >
            <Statistic
              title="问题订单"
              value={
                problemOrderLoading
                  ? "-"
                  : problemOrderCount !== null
                  ? problemOrderCount
                  : "N/A"
              }
              valueStyle={{
                color:
                  problemOrderCount && problemOrderCount > 0
                    ? "#cf1322"
                    : undefined,
              }}
              prefix={<ExclamationCircleOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {problemOrderCount && problemOrderCount > 0
                ? "点击查看并处理"
                : "暂无问题订单"}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={12} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="待处理运单"
              value={23}
              valueStyle={{ color: "#cf1322" }}
              // prefix={<ExclamationCircleOutlined />} // 如果需要可取消注释并导入
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              需尽快处理
            </Text>
          </Card>
        </Col>
        {/* <Col xs={24} sm={12} md={12} lg={6}>
          <Card bordered={false}>
            <Statistic title="在途总数" value={1289} suffix="单" />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              全球运输中
            </Text>
          </Card>
        </Col> */}
      </Row>

      {/* 2. 快捷入口 和 待办事项 */}
      <Row gutter={[16, 16]} className={styles.actionsRow}>
        <Col xs={24} lg={8}>
          <Card
            title="快捷入口"
            bordered={false}
            className={styles.fullHeightCard}
          >
            <Space direction="vertical" style={{ width: "100%" }}>
              <Button type="primary" icon={<PlusOutlined />} block>
                创建新订单
              </Button>
              <Button icon={<SearchOutlined />} block>
                查询运单
              </Button>
              <Button icon={<FileTextOutlined />} block>
                录入包裹
              </Button>
              <Button block>查看库存</Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={16}>
          <Card
            title="待办事项 / 任务列表"
            bordered={false}
            className={styles.fullHeightCard}
          >
            <List
              itemLayout="horizontal"
              dataSource={todoData}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="link" size="small">
                      查看
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<UserOutlined />} // Can use a more specific icon
                    title={<a href="#">{item.title}</a>}
                    description={`优先级: ${item.priority.toUpperCase()} - 状态: ${
                      item.status
                    }`}
                  />
                  {item.priority === "high" && <Tag color="red">高</Tag>}
                  {item.priority === "medium" && <Tag color="orange">中</Tag>}
                  {item.priority === "low" && <Tag color="blue">低</Tag>}
                </List.Item>
              )}
            />
            <Button type="link" style={{ float: "right", marginTop: "10px" }}>
              查看全部
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 3. 图表展示区 */}
      <Row gutter={[16, 16]} className={styles.chartsRow}>
        <Col xs={24} lg={12}>
          <Card title="运单状态分布" bordered={false}>
            <PieChartPlaceholder />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="近期订单量趋势" bordered={false}>
            <LineChartPlaceholder />
          </Card>
        </Col>
      </Row>

      {/* 4. 最新动态 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="最新动态" bordered={false}>
            <List
              dataSource={activityData}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />} // More dynamic avatar if available
                    title={`${item.user} ${item.action}`}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
            <Button type="link" style={{ float: "right", marginTop: "10px" }}>
              查看更多
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 5. 全球货运地图 (可选) - 占位符 */}
      {/*
       <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col span={24}>
                <Card title="全球货运地图" bordered={false}>
                    <div style={{ height: '300px', background: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Text type="secondary">(地图可视化区域)</Text>
                    </div>
                </Card>
            </Col>
       </Row>
       */}
    </div>
  );
};

export default Dashboard;
