import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Divider,
  Spin,
} from "antd";
import {
  CalendarOutlined,
  DollarOutlined,
  PlusOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import {
  fetchBillingCycles,
  createBillingCycle,
  CreateBillingCycleRequest,
  BillingCycle,
  BillingCycleStatus,
  formatBillingCycleStatus,
  generateYearOptions,
} from "../../services/billingCycleService";

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 月份信息接口
interface MonthInfo {
  month: number;
  name: string;
  exists: boolean;
  billingCycle?: BillingCycle;
}

const BillingCycleManagementPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 主要状态
  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );
  const [monthsInfo, setMonthsInfo] = useState<MonthInfo[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [loadingMonths, setLoadingMonths] = useState(false);
  const [creating, setCreating] = useState(false);

  // 统计数据状态
  const [statistics, setStatistics] = useState({
    totalCycles: 0,
    pendingCycles: 0,
    completedCycles: 0,
    totalBilledAmount: 0,
  });

  // 月份名称映射
  const monthNames = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ];

  // 初始化月份信息
  const initializeMonthsInfo = (): MonthInfo[] => {
    return monthNames.map((name, index) => ({
      month: index + 1,
      name,
      exists: false,
    }));
  };

  // 加载指定年份的账单批次信息
  const loadYearBillingCycles = async (year: number) => {
    try {
      setLoadingMonths(true);
      const response = await fetchBillingCycles({
        cycleYear: year,
        page: 1,
        pageSize: 12, // 最多12个月
      });

      const existingCycles = response.list || [];
      const newMonthsInfo = initializeMonthsInfo();

      // 标记已存在的月份
      existingCycles.forEach((cycle: BillingCycle) => {
        const monthIndex = cycle.cycleMonth - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
          newMonthsInfo[monthIndex].exists = true;
          newMonthsInfo[monthIndex].billingCycle = cycle;
        }
      });

      setMonthsInfo(newMonthsInfo);

      // 计算统计数据
      calculateStatistics(existingCycles);
    } catch (error) {
      console.error("加载年份账单批次失败:", error);
      message.error("加载年份信息失败");
      setMonthsInfo(initializeMonthsInfo());
    } finally {
      setLoadingMonths(false);
    }
  };

  // 计算统计数据
  const calculateStatistics = (cycles: BillingCycle[]) => {
    const stats = {
      totalCycles: cycles.length,
      pendingCycles: 0,
      completedCycles: 0,
      totalBilledAmount: 0,
    };

    cycles.forEach((cycle) => {
      if (cycle.status === BillingCycleStatus.PENDING) {
        stats.pendingCycles += 1;
      }
      if (cycle.status === BillingCycleStatus.COMPLETED) {
        stats.completedCycles += 1;
      }
      if (cycle.totalBilledAmount) {
        stats.totalBilledAmount += cycle.totalBilledAmount;
      }
    });

    setStatistics(stats);
  };

  // 年份变化时重新加载
  const handleYearChange = (year: number) => {
    setSelectedYear(year);
    setSelectedMonth(null);
    form.resetFields(["cycleName", "notes"]);
    loadYearBillingCycles(year);
  };

  // 选择月份
  const handleMonthSelect = (monthInfo: MonthInfo) => {
    if (monthInfo.exists) {
      // 如果已存在，进入账单详情页面
      if (monthInfo.billingCycle) {
        navigate(`/billing-finance/cycles/${monthInfo.billingCycle.id}/bills`);
      }
      return;
    }

    // 如果不存在，选中该月份准备创建
    setSelectedMonth(monthInfo.month);
    // 自动生成账单批次名称
    const cycleName = `${selectedYear}年${monthInfo.month}月`;
    form.setFieldsValue({ cycleName });
  };

  // 创建账单批次
  const handleCreateBillingCycle = async () => {
    if (!selectedMonth) {
      message.warning("请选择要创建的月份");
      return;
    }

    try {
      const values = await form.validateFields();
      setCreating(true);

      const params: CreateBillingCycleRequest = {
        cycleYear: selectedYear,
        cycleMonth: selectedMonth,
        cycleName: values.cycleName,
        notes: values.notes,
      };

      await createBillingCycle(params);
      message.success("账单批次创建成功");
      form.resetFields();
      setSelectedMonth(null);

      // 重新加载当前年份数据
      loadYearBillingCycles(selectedYear);
    } catch (error) {
      console.error("创建账单批次失败:", error);
    } finally {
      setCreating(false);
    }
  };

  // 取消创建
  const handleCancelCreate = () => {
    setSelectedMonth(null);
    form.resetFields();
  };

  // 渲染账单批次状态标签
  const renderStatusTag = (status: BillingCycleStatus) => {
    const statusConfig = {
      [BillingCycleStatus.PENDING]: {
        color: "orange",
        icon: <ClockCircleOutlined />,
      },
      [BillingCycleStatus.PROCESSING]: {
        color: "blue",
        icon: <ClockCircleOutlined />,
      },
      [BillingCycleStatus.COMPLETED]: {
        color: "green",
        icon: <FileTextOutlined />,
      },
      [BillingCycleStatus.FAILED]: { color: "red", icon: <FileTextOutlined /> },
      [BillingCycleStatus.CANCELLED]: {
        color: "default",
        icon: <FileTextOutlined />,
      },
    };

    const config = statusConfig[status];
    return (
      <Tag color={config.color} icon={config.icon}>
        {formatBillingCycleStatus(status)}
      </Tag>
    );
  };

  // 初始化加载数据
  useEffect(() => {
    const currentYear = new Date().getFullYear();
    setSelectedYear(currentYear);
    loadYearBillingCycles(currentYear);
  }, []);

  return (
    <div style={{ padding: "24px" }}>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px",
        }}
      >
        <Title level={2} style={{ margin: 0 }}>
          账单批次管理
        </Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: "24px" }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="账单批次总数"
              value={statistics.totalCycles}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待处理批次"
              value={statistics.pendingCycles}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: "#fa8c16" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成批次"
              value={statistics.completedCycles}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总账单金额"
              value={statistics.totalBilledAmount}
              precision={2}
              prefix={<DollarOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 年份选择 */}
        <div style={{ marginBottom: "24px" }}>
          <Title level={4}>选择年份</Title>
          <Select
            value={selectedYear}
            onChange={handleYearChange}
            style={{ width: 200 }}
            loading={loadingMonths}
          >
            {generateYearOptions().map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        <Divider />

        {/* 月份选择 */}
        <div style={{ marginBottom: "24px" }}>
          <Title level={4}>选择月份</Title>
          <Text
            type="secondary"
            style={{ display: "block", marginBottom: "16px" }}
          >
            绿色表示已存在的账单批次（点击进入详情），灰色表示可以创建的月份
          </Text>

          <Spin spinning={loadingMonths}>
            <Row gutter={[16, 16]}>
              {monthsInfo.map((monthInfo) => (
                <Col span={6} key={monthInfo.month}>
                  <Card
                    size="small"
                    hoverable
                    style={{
                      cursor: "pointer",
                      border:
                        selectedMonth === monthInfo.month
                          ? "2px solid #1890ff"
                          : undefined,
                      backgroundColor: monthInfo.exists ? "#f6ffed" : undefined,
                    }}
                    onClick={() => handleMonthSelect(monthInfo)}
                    bodyStyle={{ padding: "12px", textAlign: "center" }}
                  >
                    <Space
                      direction="vertical"
                      size="small"
                      style={{ width: "100%" }}
                    >
                      <Text strong>{monthInfo.name}</Text>
                      {monthInfo.exists ? (
                        <>
                          <Tag color="success" icon={<CheckOutlined />}>
                            已存在
                          </Tag>
                          {monthInfo.billingCycle && (
                            <>
                              <Text
                                type="secondary"
                                style={{ fontSize: "12px" }}
                              >
                                {monthInfo.billingCycle.cycleName}
                              </Text>
                              {renderStatusTag(monthInfo.billingCycle.status)}
                              {monthInfo.billingCycle.totalBilledAmount !==
                                null &&
                                monthInfo.billingCycle.totalBilledAmount !==
                                  undefined && (
                                  <Text
                                    style={{
                                      fontSize: "12px",
                                      color: "#1890ff",
                                    }}
                                  >
                                    ¥
                                    {monthInfo.billingCycle.totalBilledAmount.toFixed(
                                      2
                                    )}
                                  </Text>
                                )}
                            </>
                          )}
                        </>
                      ) : (
                        <Tag color="default" icon={<PlusOutlined />}>
                          可创建
                        </Tag>
                      )}
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Spin>
        </div>

        {/* 选中月份的创建表单 */}
        {selectedMonth && (
          <>
            <Divider />
            <div>
              <Title level={4}>创建账单批次</Title>
              <Text
                type="secondary"
                style={{ display: "block", marginBottom: "16px" }}
              >
                为 {selectedYear} 年 {selectedMonth} 月创建账单批次
              </Text>

              <Form form={form} layout="vertical" style={{ maxWidth: 600 }}>
                <Form.Item
                  name="cycleName"
                  label="账单批次名称"
                  rules={[
                    { required: true, message: "请输入账单批次名称" },
                    { max: 100, message: "账单批次名称不能超过100个字符" },
                  ]}
                >
                  <Input placeholder="请输入账单批次名称" />
                </Form.Item>

                <Form.Item
                  name="notes"
                  label="备注信息"
                  rules={[{ max: 500, message: "备注信息不能超过500个字符" }]}
                >
                  <TextArea
                    placeholder="请输入备注信息（可选）"
                    rows={3}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      onClick={handleCreateBillingCycle}
                      loading={creating}
                      icon={<PlusOutlined />}
                    >
                      创建账单批次
                    </Button>
                    <Button onClick={handleCancelCreate}>取消</Button>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </>
        )}
      </Card>
    </div>
  );
};

export default BillingCycleManagementPage;
