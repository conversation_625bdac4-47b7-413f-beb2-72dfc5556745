import React, { useState } from "react";
import {
  Upload,
  Button,
  message,
  Image,
  Card,
  Space,
  Typography,
  Popconfirm,
  Tooltip,
  Progress,
} from "antd";
import { UploadOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import {
  uploadImage,
  deleteImage,
  validateImageFile,
  BusinessType,
  formatFileSize,
  extractFileNameFromUrl,
  type ImageUploadData,
} from "../../services/uploadService";

const { Text } = Typography;

// 上传文件状态接口
interface UploadedFile {
  uid: string;
  name: string;
  url: string;
  size: number;
  uploadTime: string;
}

// 组件属性接口
interface ImageUploadProps {
  value?: string[]; // 当前的图片URL数组
  onChange?: (urls: string[]) => void; // URL变化回调
  businessType: BusinessType; // 业务类型
  maxCount?: number; // 最大上传数量，默认为1
  disabled?: boolean; // 是否禁用
  placeholder?: string; // 占位符文本
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = [],
  onChange,
  businessType,
  maxCount = 1,
  disabled = false,
  placeholder = "点击上传图片",
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(() => {
    // 初始化已上传文件列表
    return value.map((url, index) => ({
      uid: `initial-${index}`,
      name: extractFileNameFromUrl(url),
      url,
      size: 0, // 初始文件大小未知
      uploadTime: "", // 初始上传时间未知
    }));
  });

  /**
   * 处理文件上传前的验证
   */
  const beforeUpload = (file: File): boolean => {
    // 检查数量限制
    if (uploadedFiles.length >= maxCount) {
      message.error(`最多只能上传${maxCount}张图片`);
      return false;
    }

    // 验证文件
    const error = validateImageFile(file);
    if (error) {
      message.error(error);
      return false;
    }

    // 开始上传
    handleUpload(file);
    return false; // 阻止自动上传，使用自定义上传逻辑
  };

  /**
   * 处理文件上传
   */
  const handleUpload = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 100);

      // 调用上传接口
      const uploadResult: ImageUploadData = await uploadImage(
        file,
        businessType
      );

      // 清除进度定时器
      clearInterval(progressInterval);
      setUploadProgress(100);

      // 创建新的文件记录
      const newFile: UploadedFile = {
        uid: `upload-${Date.now()}`,
        name: file.name,
        url: uploadResult.fileUrl,
        size: uploadResult.fileSize,
        uploadTime: uploadResult.uploadTime,
      };

      // 更新文件列表
      const newFileList = [...uploadedFiles, newFile];
      setUploadedFiles(newFileList);

      // 通知外部组件URL变化
      const newUrls = newFileList.map((f) => f.url);
      onChange?.(newUrls);

      message.success("图片上传成功");
    } catch (error) {
      console.error("图片上传失败:", error);
      message.error("图片上传失败，请重试");
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  /**
   * 处理文件删除
   */
  const handleDelete = async (file: UploadedFile) => {
    try {
      // 调用删除接口（如果是新上传的文件）
      if (!file.uid.startsWith("initial-")) {
        await deleteImage(file.url);
      }

      // 更新文件列表
      const newFileList = uploadedFiles.filter((f) => f.uid !== file.uid);
      setUploadedFiles(newFileList);

      // 通知外部组件URL变化
      const newUrls = newFileList.map((f) => f.url);
      onChange?.(newUrls);

      message.success("图片删除成功");
    } catch (error) {
      console.error("图片删除失败:", error);
      message.error("图片删除失败，请重试");
    }
  };

  /**
   * 渲染上传按钮
   */
  const renderUploadButton = () => {
    if (uploadedFiles.length >= maxCount) {
      return null;
    }

    return (
      <Upload
        beforeUpload={beforeUpload}
        showUploadList={false}
        disabled={disabled || uploading}
        accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
      >
        <Button
          icon={<UploadOutlined />}
          loading={uploading}
          disabled={disabled}
          style={{ width: "100%" }}
        >
          {uploading ? `上传中 ${uploadProgress}%` : placeholder}
        </Button>
      </Upload>
    );
  };

  /**
   * 渲染文件列表
   */
  const renderFileList = () => {
    if (uploadedFiles.length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: "12px" }}>
        <Space direction="vertical" style={{ width: "100%" }}>
          {uploadedFiles.map((file) => (
            <Card
              key={file.uid}
              size="small"
              style={{ padding: "8px" }}
              bodyStyle={{ padding: "8px" }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Space>
                  {/* 直接显示图片缩略图 */}
                  <div
                    style={{
                      width: "40px",
                      height: "40px",
                      border: "1px solid #d9d9d9",
                      borderRadius: "4px",
                      overflow: "hidden",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                    }}
                  >
                    <Image
                      src={file.url}
                      alt={file.name}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                      preview={{
                        mask: <EyeOutlined style={{ color: "#fff" }} />,
                      }}
                      fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAxNkMyMS4xMDQ2IDE2IDIyIDE2Ljg5NTQgMjIgMThDMjIgMTkuMTA0NiAyMS4xMDQ2IDIwIDIwIDIwQzE4Ljg5NTQgMjAgMTggMTkuMTA0NiAxOCAxOEMxOCAxNi44OTU0IDE4Ljg5NTQgMTYgMjAgMTZaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik0xNCAyNEwyMCAyMEwyNiAyNEgxNFoiIGZpbGw9IiNCRkJGQkYiLz4KPHN2Zz4K"
                    />
                  </div>
                  <div>
                    <Text
                      strong
                      style={{
                        fontSize: "14px",
                        maxWidth: "200px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        display: "inline-block",
                      }}
                    >
                      {file.name}
                    </Text>
                    {file.size > 0 && (
                      <div>
                        <Text type="secondary" style={{ fontSize: "12px" }}>
                          {formatFileSize(file.size)}
                          {file.uploadTime && ` · ${file.uploadTime}`}
                        </Text>
                      </div>
                    )}
                  </div>
                </Space>
                <Space>
                  <Popconfirm
                    title="确认删除"
                    description="确定要删除这张图片吗？"
                    onConfirm={() => handleDelete(file)}
                    okText="删除"
                    cancelText="取消"
                    disabled={disabled}
                  >
                    <Tooltip title="删除图片">
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        danger
                        disabled={disabled}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              </div>
            </Card>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <div>
      {/* 上传进度条 */}
      {uploading && (
        <Progress
          percent={uploadProgress}
          size="small"
          style={{ marginBottom: "12px" }}
          status="active"
        />
      )}

      {/* 上传按钮 */}
      {renderUploadButton()}

      {/* 文件列表 */}
      {renderFileList()}

      {/* 提示信息 */}
      <div style={{ marginTop: "8px" }}>
        <Text type="secondary" style={{ fontSize: "12px" }}>
          支持jpg、png等格式，单个文件最大10MB
          {maxCount > 1 && `，最多上传${maxCount}张图片`}
          {uploadedFiles.length > 0 && " · 点击图片可放大预览"}
        </Text>
      </div>
    </div>
  );
};

export default ImageUpload;
