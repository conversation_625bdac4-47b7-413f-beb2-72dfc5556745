import React, { useState, useEffect, useCallback, useRef } from "react";
import { Button, message, Modal, Alert, Space, Tabs, Typography } from "antd";
import type { TabsProps } from "antd";
import {
  PlusOutlined,
  ReloadOutlined,
  ImportOutlined,
  ExportOutlined,
  PrinterOutlined,
  ProfileOutlined,
  DeleteOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import {
  Manifest,
  ManifestQueryParams,
  searchManifests,
  exportManifests,
} from "../../services/manifestService";
import SearchForm, {
  SearchFormRef,
} from "../../components/manifest/SearchForm";
import type { SearchParams } from "../../components/manifest/SearchForm";
import ManifestTable from "../../components/manifest/ManifestTable";
import ManifestDetailDrawer from "../../components/manifest/ManifestDetailDrawer";

const { Title } = Typography;

const ManifestListPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<Manifest[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchParams, setSearchParams] = useState<ManifestQueryParams>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [selectedManifestId, setSelectedManifestId] = useState<number | null>(
    null
  );
  const [scrollToAdjustments, setScrollToAdjustments] =
    useState<boolean>(false);
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [activeTabKey, setActiveTabKey] = useState<string>("all");
  const searchFormRef = useRef<SearchFormRef>(null);

  // 加载数据
  const loadData = useCallback(
    async (params: ManifestQueryParams) => {
      try {
        setLoading(true);
        // 传入的 params 应该已经包含了正确的 status (如果需要)
        const queryParams = { ...params };
        // 移除下面这部分逻辑，因为调用方会确保 status 的正确性
        // if (activeTabKey !== "all") {
        //   queryParams.status = Number(activeTabKey);
        // }

        const result = await searchManifests({
          ...queryParams,
          page: params.page || current,
          pageSize: params.pageSize || pageSize,
        });
        setData(result.list);
        setTotal(result.total);
      } catch (error) {
        console.error("获取运单列表失败:", error);
        message.error("获取运单列表失败，请稍后重试");
      } finally {
        setLoading(false);
      }
    },
    [current, pageSize, activeTabKey]
  );

  // 首次加载时执行一次加载数据
  useEffect(() => {
    loadData(searchParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 移除对searchParams的依赖，避免重复加载

  // 处理页码变化
  const handlePageChange = (page: number, pageSize: number) => {
    setCurrent(page);
    setPageSize(pageSize);

    // 根据当前激活的Tab，合并status参数
    const queryParams: ManifestQueryParams = { ...searchParams };
    if (activeTabKey !== "all") {
      queryParams.status = Number(activeTabKey);
    } else if (queryParams.status !== undefined) {
      // 如果是"全部"tab，确保移除status参数
      delete queryParams.status;
    }

    loadData({ ...queryParams, page, pageSize });
  };

  // 处理搜索
  const handleSearch = (params: SearchParams) => {
    setCurrent(1); // 搜索时重置为第一页

    // 根据当前激活的Tab，合并status参数
    const queryParams: ManifestQueryParams = { ...params };
    if (activeTabKey !== "all") {
      queryParams.status = Number(activeTabKey);
    }

    setSearchParams(queryParams); // 更新搜索参数，保持状态同步

    // 直接调用loadData，不等待useEffect触发
    loadData({ ...queryParams, page: 1, pageSize });
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    setActiveTabKey(key);
    setCurrent(1); // 切换Tab时重置为第一页

    // 清除搜索条件中的状态筛选，由Tab控制状态
    const newParams = { ...searchParams };
    delete newParams.status;
    setSearchParams(newParams);

    // 直接调用loadData，合并当前Tab状态
    const queryParams = { ...newParams };
    if (key !== "all") {
      queryParams.status = Number(key);
    }
    loadData({ ...queryParams, page: 1, pageSize });
  };

  // 查看运单详情
  const handleViewDetail = (id: number) => {
    setSelectedManifestId(id);
    setDetailVisible(true);
  };

  // 关闭详情抽屉
  const handleCloseDetail = () => {
    setDetailVisible(false);
    setSelectedManifestId(null);
    setScrollToAdjustments(false); // 重置滚动状态
  };

  // 打开创建运单弹窗
  const handleOpenCreateModal = () => {
    setCreateModalVisible(true);
  };

  // 关闭创建运单弹窗
  const handleCloseCreateModal = () => {
    setCreateModalVisible(false);
  };

  // 打印面单
  const handlePrint = (id: number) => {
    message.info(`打印面单 ID: ${id}`);
    // 实际应用中调用打印服务
  };

  // 更新追踪状态
  const handleUpdateTracking = (id: number) => {
    message.info(`更新追踪状态 ID: ${id}`);
    // 实际应用中调用更新API
  };

  // 标记异常
  const handleMarkException = (id: number) => {
    message.info(`标记异常 ID: ${id}`);
    // 实际应用中打开异常标记弹窗
  };

  // 调整费用
  const handleAdjustFee = (id: number) => {
    setSelectedManifestId(id);
    setScrollToAdjustments(true);
    setDetailVisible(true);
  };

  // 选择行变化
  const handleSelectChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 清除选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
  };

  // 批量打印
  const handleBatchPrint = () => {
    message.info(`批量打印 ${selectedRowKeys.length} 条运单`);
    // 实际应用中调用批量打印服务
  };

  // 批量导出
  const handleBatchExport = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择需要导出的运单");
      return;
    }

    try {
      setExportLoading(true);
      message.info(`导出 ${selectedRowKeys.length} 条运单`);
      // 实际应用中调用导出API
      // 模拟导出成功
      setTimeout(() => {
        message.success("导出成功");
        setExportLoading(false);
      }, 2000);
    } catch (error) {
      console.error("导出运单失败:", error);
      message.error("导出失败，请稍后重试");
      setExportLoading(false);
    }
  };

  // 批量分配到提单
  const handleBatchAssignToMasterBill = () => {
    message.info(`分配 ${selectedRowKeys.length} 条运单到提单`);
    // 实际应用中打开分配弹窗
  };

  // 批量删除
  const handleBatchDelete = () => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除所选的 ${selectedRowKeys.length} 条运单吗？此操作不可恢复。`,
      okText: "确认",
      okType: "danger",
      cancelText: "取消",
      onOk: () => {
        message.success(`已删除 ${selectedRowKeys.length} 条运单`);
        setSelectedRowKeys([]);

        // 构造查询参数，合并当前Tab状态
        const queryParams = { ...searchParams };
        if (activeTabKey !== "all") {
          queryParams.status = Number(activeTabKey);
        }
        loadData({ ...queryParams, page: current, pageSize });
      },
    });
  };

  // 导出所有符合条件的运单
  const handleExportAll = async () => {
    try {
      setExportLoading(true);
      // 实际应用中，调用导出API
      const blob = await exportManifests(searchParams);

      // 创建下载链接并自动下载
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `运单列表_${new Date().toISOString().split("T")[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success("导出成功");
    } catch (error) {
      console.error("导出运单失败:", error);
      message.error("导出失败，请稍后重试");
    } finally {
      setExportLoading(false);
    }
  };

  // 定义Tab项
  const tabItems: TabsProps["items"] = [
    {
      key: "all",
      label: "全部",
    },
    {
      key: "0",
      label: "待审核",
    },
    {
      key: "1",
      label: "已预报",
    },
    {
      key: "2",
      label: "已揽件",
    },
    {
      key: "3",
      label: "已发货",
    },
    {
      key: "4",
      label: "已送达",
    },
  ];

  // 处理页面中查询按钮点击
  const handleQueryClick = () => {
    // 立即设置loading状态
    setLoading(true);

    // 直接调用SearchForm组件的submitForm方法
    if (searchFormRef.current) {
      searchFormRef.current.submitForm();
    }
  };

  // 处理页面中重置按钮点击
  const handleResetClick = () => {
    if (searchFormRef.current) {
      searchFormRef.current.resetForm();
    }
  };

  // 处理刷新按钮点击
  const handleRefreshClick = () => {
    // 构造查询参数，合并当前Tab状态
    const queryParams = { ...searchParams };
    if (activeTabKey !== "all") {
      queryParams.status = Number(activeTabKey);
    }
    loadData({ ...queryParams, page: current, pageSize });
  };

  return (
    <div className="manifest-list-page">
      <Title level={4}>运单管理</Title>

      {/* 状态Tab页 */}
      <Tabs
        activeKey={activeTabKey}
        onChange={handleTabChange}
        items={tabItems}
        type="card"
        className="status-tabs"
      />

      {/* 搜索表单和操作按钮区域的容器 */}
      <div style={{ marginBottom: 16, marginTop: 16 }}>
        {/* 搜索表单与操作按钮的一行 */}
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          {/* 左侧搜索区域 */}
          <div style={{ flex: 1 }}>
            <SearchForm
              ref={searchFormRef}
              onSearch={handleSearch}
              loading={loading}
              setLoading={setLoading}
              buttonPosition="none" // 不在表单中显示按钮
            />
          </div>

          {/* 右侧按钮区域 */}
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "16px",
              marginLeft: "16px",
              alignItems: "center", // 居中对齐按钮
            }}
          >
            {/* 查询和重置按钮 */}
            <Space
              size="small"
              style={{ display: "flex", justifyContent: "center" }}
            >
              <Button
                type="primary"
                icon={<SearchOutlined />}
                loading={loading}
                onClick={handleQueryClick}
              >
                查询
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleResetClick}>
                重置
              </Button>
            </Space>

            {/* 刷新按钮 */}
            <Button onClick={handleRefreshClick} icon={<ReloadOutlined />}>
              刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: "flex", justifyContent: "flex-start" }}>
          {/* 左侧基本操作按钮 */}
          <Space size="small">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleOpenCreateModal}
            >
              新建运单
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => message.info("批量导入功能开发中...")}
            >
              批量导入
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExportAll}
              loading={exportLoading}
              disabled={total === 0}
            >
              导出全部
            </Button>
          </Space>
        </div>
      </div>

      {/* 选中项提示和批量操作区域 */}
      {selectedRowKeys.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <Alert
            message={
              <span>
                已选择 <strong>{selectedRowKeys.length}</strong> 项
                <Button type="link" size="small" onClick={handleClearSelection}>
                  清除
                </Button>
              </span>
            }
            type="info"
            showIcon
            style={{ marginBottom: 12 }}
          />

          {/* 批量操作按钮组 */}
          <Space size="small">
            <Button icon={<PrinterOutlined />} onClick={handleBatchPrint}>
              批量打印
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleBatchExport}
              loading={exportLoading}
            >
              导出选中
            </Button>
            <Button
              icon={<ProfileOutlined />}
              onClick={handleBatchAssignToMasterBill}
            >
              分配到提单
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleBatchDelete}
            >
              批量删除
            </Button>
          </Space>
        </div>
      )}

      {/* 数据表格 */}
      <ManifestTable
        data={data}
        loading={loading}
        total={total}
        current={current}
        pageSize={pageSize}
        selectedRowKeys={selectedRowKeys}
        onSelectChange={handleSelectChange}
        onPageChange={handlePageChange}
        onViewDetail={handleViewDetail}
        onPrint={handlePrint}
        onUpdateTracking={handleUpdateTracking}
        onMarkException={handleMarkException}
        onAdjustFee={handleAdjustFee}
      />

      {/* 运单详情抽屉 */}
      <ManifestDetailDrawer
        manifestId={selectedManifestId}
        visible={detailVisible}
        onClose={handleCloseDetail}
        scrollToAdjustments={scrollToAdjustments}
      />

      {/* 新建运单弹窗 */}
      <Modal
        title="新建运单"
        open={createModalVisible}
        onCancel={handleCloseCreateModal}
        footer={[
          <Button key="cancel" onClick={handleCloseCreateModal}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleCloseCreateModal}>
            创建
          </Button>,
        ]}
        width={800}
      >
        <p>新建运单表单将在这里实现...</p>
      </Modal>
    </div>
  );
};

export default ManifestListPage;
