import React, { useState, useEffect } from "react";
import {
  Modal,
  Table,
  Button,
  Tag,
  Typography,
  Tooltip,
  Progress,
  message,
} from "antd";
import {
  ReloadOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  UserOutlined,
  PlayCircleOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { TablePaginationConfig } from "antd/es/table";
import {
  fetchBillingTaskList,
  BillingTaskItem,
  BillingTaskListQueryParams,
  BillingTaskStatus,
  formatBillingTaskStatus,
} from "../../services/billingService";

const { Title } = Typography;

interface BillTaskListModalProps {
  visible: boolean;
  onClose: () => void;
  billingCycleId?: number;
  cycleName?: string;
}

// 任务列表状态接口
interface TaskListState {
  loading: boolean;
  data: BillingTaskItem[];
  total: number;
  pagination: {
    current: number;
    pageSize: number;
  };
}

const BillTaskListModal: React.FC<BillTaskListModalProps> = ({
  visible,
  onClose,
  billingCycleId,
  cycleName,
}) => {
  const [state, setState] = useState<TaskListState>({
    loading: false,
    data: [],
    total: 0,
    pagination: {
      current: 1,
      pageSize: 10,
    },
  });

  /**
   * 加载任务列表
   */
  const loadTaskList = async (params?: BillingTaskListQueryParams) => {
    if (!billingCycleId) return;

    setState((prev) => ({ ...prev, loading: true }));

    try {
      const queryParams: BillingTaskListQueryParams = {
        billingCycleId: billingCycleId,
        page: state.pagination.current,
        pageSize: state.pagination.pageSize,
        ...params,
      };

      const result = await fetchBillingTaskList(queryParams);

      setState((prev) => ({
        ...prev,
        data: result.list,
        total: result.total,
        loading: false,
      }));
    } catch (error) {
      console.error("加载任务列表失败:", error);
      message.error("加载任务列表失败，请重试");
      setState((prev) => ({ ...prev, loading: false }));
    }
  };

  /**
   * 刷新任务列表
   */
  const handleRefresh = () => {
    loadTaskList();
  };

  /**
   * 渲染任务状态标签
   */
  const renderTaskStatusTag = (status: BillingTaskStatus) => {
    const statusConfig = {
      [BillingTaskStatus.PENDING]: {
        color: "orange",
        icon: <ClockCircleOutlined />,
      },
      [BillingTaskStatus.PROCESSING]: {
        color: "blue",
        icon: <PlayCircleOutlined />,
      },
      [BillingTaskStatus.COMPLETED]: {
        color: "green",
        icon: <CheckOutlined />,
      },
      [BillingTaskStatus.FAILED]: {
        color: "red",
        icon: <CloseOutlined />,
      },
    };

    const config = statusConfig[status];
    return (
      <Tag color={config.color} icon={config.icon}>
        {formatBillingTaskStatus(status)}
      </Tag>
    );
  };

  /**
   * 任务列表表格列定义
   */
  const columns: ColumnsType<BillingTaskItem> = [
    {
      title: "任务ID",
      dataIndex: "taskId",
      key: "taskId",
      width: 200,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: "monospace", fontSize: "12px" }}>
            {text.substring(0, 8)}...
          </span>
        </Tooltip>
      ),
    },
    {
      title: "目标客户",
      dataIndex: "targetCustomerIds",
      key: "targetCustomerIds",
      width: 120,
      render: (customerIds: string) => {
        const ids = customerIds.split(",");
        const count = ids.length;
        return (
          <div>
            <Tag color="blue" icon={<UserOutlined />}>
              {count} 个客户
            </Tag>
            {count <= 3 ? (
              <div
                style={{ fontSize: "12px", color: "#999", marginTop: "4px" }}
              >
                ID: {customerIds}
              </div>
            ) : (
              <Tooltip title={`客户ID: ${customerIds}`}>
                <div
                  style={{ fontSize: "12px", color: "#999", marginTop: "4px" }}
                >
                  ID: {ids.slice(0, 3).join(",")}...
                </div>
              </Tooltip>
            )}
          </div>
        );
      },
    },
    {
      title: "任务状态",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: BillingTaskStatus) => renderTaskStatusTag(status),
    },
    {
      title: "执行进度",
      key: "progress",
      width: 150,
      render: (_, record) => (
        <div>
          <Progress
            percent={record.progressPercentage}
            size="small"
            status={
              record.status === BillingTaskStatus.FAILED
                ? "exception"
                : record.status === BillingTaskStatus.COMPLETED
                ? "success"
                : "active"
            }
          />
          <div style={{ fontSize: "12px", color: "#999", marginTop: "4px" }}>
            {record.itemsProcessedCount}/{record.totalItemsToProcess} 项
          </div>
        </div>
      ),
    },
    {
      title: "提交信息",
      key: "submitter",
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.submittedByNickname || "系统"}</div>
          <div style={{ fontSize: "12px", color: "#999" }}>
            {record.submitTime}
          </div>
        </div>
      ),
    },
    {
      title: "执行时间",
      key: "execution",
      width: 160,
      render: (_, record) => (
        <div>
          {record.startTime && (
            <div style={{ fontSize: "12px" }}>开始: {record.startTime}</div>
          )}
          {record.endTime && (
            <div style={{ fontSize: "12px" }}>结束: {record.endTime}</div>
          )}
          {record.duration !== undefined && (
            <div style={{ fontSize: "12px", color: "#1890ff" }}>
              耗时: {record.duration}秒
            </div>
          )}
        </div>
      ),
    },
    {
      title: "错误信息",
      dataIndex: "errorMessage",
      key: "errorMessage",
      width: 200,
      ellipsis: true,
      render: (errorMessage: string) =>
        errorMessage ? (
          <Tooltip title={errorMessage}>
            <span style={{ color: "#ff4d4f", fontSize: "12px" }}>
              {errorMessage}
            </span>
          </Tooltip>
        ) : (
          "-"
        ),
    },
  ];

  /**
   * 处理分页变化
   */
  const handleTableChange = (pagination: TablePaginationConfig) => {
    const newPagination = {
      current: pagination.current || 1,
      pageSize: pagination.pageSize || 10,
    };

    setState((prev) => ({
      ...prev,
      pagination: newPagination,
    }));

    // 重新查询任务列表
    const queryParams: BillingTaskListQueryParams = {
      billingCycleId: billingCycleId!,
      page: newPagination.current,
      pageSize: newPagination.pageSize,
    };

    loadTaskList(queryParams);
  };

  // 当弹窗打开时加载数据
  useEffect(() => {
    if (visible && billingCycleId) {
      loadTaskList();
    }
  }, [visible, billingCycleId]);

  return (
    <Modal
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            paddingRight: "40px",
          }}
        >
          <Title level={4} style={{ margin: 0 }}>
            账单生成任务列表
            {cycleName && (
              <span
                style={{ fontSize: "14px", color: "#666", marginLeft: "8px" }}
              >
                - {cycleName}
              </span>
            )}
          </Title>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={state.loading}
            size="small"
          >
            刷新
          </Button>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Table
        columns={columns}
        dataSource={state.data}
        rowKey="taskId"
        loading={state.loading}
        pagination={{
          current: state.pagination.current,
          pageSize: state.pagination.pageSize,
          total: state.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `显示 ${range[0]}-${range[1]} 条记录，共 ${total} 条`,
          pageSizeOptions: ["10", "20", "50"],
        }}
        onChange={handleTableChange}
        scroll={{ y: 400 }}
        size="middle"
      />
    </Modal>
  );
};

export default BillTaskListModal;
