/* src/pages/OrderForecast/OrderForecast.module.css */

.forecastContainer {
  /* background-color: #fff; */ /* 内容背景已在 MainLayout 中设置 */
  /* padding: 24px; */ /* 内边距已在 MainLayout 中设置 */
}

.pageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.stepsIndicator {
  margin-bottom: 32px;
}

.stepContent {
  /* min-height: 400px; */ /* 根据内容自适应高度 */
  /* background-color: #fafafa; */ /* 可以给步骤内容区域加背景 */
  /* padding: 24px; */
}

/* Upload Step Styles */
.uploadCard {
  max-width: 800px; /* 限制上传卡片最大宽度 */
  margin: 0 auto; /* 居中显示 */
}

.dragger {
  /* background: #fafafa !important; */ /* 确保背景色 */
  padding: 40px 20px !important; /* 增加内边距 */
}

.dragger :global(.ant-upload-drag-icon .anticon) {
  font-size: 48px;
  color: #1890ff;
}

.dragger :global(.ant-upload-text) {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.85);
}

.dragger :global(.ant-upload-hint) {
  color: rgba(0, 0, 0, 0.45);
}

.progressContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.uploadActions {
  margin-top: 24px;
  text-align: center;
}

.lastUploadInfo {
  margin-top: 16px;
  text-align: center;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* Preview Step Styles */
.previewContainer {
  /* Add padding or specific layout if needed */
}

.summaryCard {
  margin-bottom: 16px;
  /* background-color: #f0f2f5; */ /* Optional background */
}

.summaryCard :global(.ant-statistic-content) {
  font-size: 20px; /* Adjust statistic value font size */
}

.tableToolbar {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

/* Row Highlighting */
.row-valid {
  /* Default antd table row style */
}
.row-warning {
  background-color: #fffbe6 !important; /* Ant Design warning color */
}
.row-error {
  background-color: #fff1f0 !important; /* Ant Design error color */
}

/* Cell Error/Warning Indication */
.errorCell,
.warningCell {
  position: relative;
  padding-right: 18px; /* Space for icon */
}

.errorCell::after {
  content: "!"; /* Or use an icon font */
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  color: #ff4d4f; /* Ant Design error color */
  font-weight: bold;
  font-size: 12px;
  border: 1px solid #ffccc7;
  background-color: #fff1f0;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  line-height: 12px;
  text-align: center;
}
.warningCell::after {
  /* Similar style for warning, different icon/color */
  content: "?";
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  color: #d46b08; /* Ant Design warning color */
  font-weight: bold;
  font-size: 12px;
  border: 1px solid #ffe58f;
  background-color: #fffbe6;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  line-height: 12px;
  text-align: center;
}

/* Error cell with red border (example) */
.errorCell {
  /* border: 1px solid #ff4d4f; */ /* Add border if needed */
}

/* Bottom Action Bar */
.bottomActionBar {
  position: sticky;
  bottom: 0;
  left: 0; /* Ensure it spans the full width within its container */
  right: 0;
  background-color: #fff;
  padding: 16px 24px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10; /* Ensure it stays above table content */
}

/* Adjust margin if MainLayout content has padding */
:global(.ant-layout-content) .bottomActionBar {
  margin: 0 -24px -24px -24px; /* Counteract parent padding */
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* Confirm Step Styles */
.confirmContainer {
  padding: 24px;
  background-color: #f9f9f9; /* 轻微的背景色区分 */
  border-radius: 8px;
}

/* 复用 .summaryCard 或根据需要添加特定样式 */
.confirmContainer .summaryCard {
  background-color: #fff; /* 确保背景为白色 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/* 确认步骤表格卡片样式（如果需要） */
.confirmTableCard {
  /* 添加特定样式，例如去除边框 */
}

/* 确认操作卡片样式（如果需要） */
.confirmActionsCard {
  background-color: transparent; /* 透明背景 */
  border: none; /* 去除边框 */
  box-shadow: none; /* 去除阴影 */
}

/* 确认步骤底部导航按钮 */
.confirmNavigation {
  margin-top: 24px;
  text-align: center;
}
