import { apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 费用调整类型接口
export interface FinancialAdjustmentType {
  value: string;
  label: string;
  description?: string;
}

// 费用调整类型项接口
interface AdjustmentTypeItem {
  id: number;
  name: string;
  description: string;
}

// 费用调整类型分页响应数据接口
interface FinancialAdjustmentTypesData {
  total: number;
  list: AdjustmentTypeItem[];
}

// 费用调整类型分页响应接口
interface FinancialAdjustmentTypesResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: FinancialAdjustmentTypesData;
}

// 标准调整类型常量
export const STANDARD_ADJUSTMENT_TYPES = {
  COMPENSATION: {
    value: "COMPENSATION",
    label: "赔偿",
    description: "快递损坏或丢失时的赔偿",
  },
  REASSIGNMENT: {
    value: "REASSIGNMENT",
    label: "再派",
    description: "包裹再次派送的费用",
  },
  DESTRUCTION: {
    value: "DESTRUCTION",
    label: "销毁",
    description: "包裹销毁的相关费用",
  },
  RETURN: {
    value: "RETURN",
    label: "退回",
    description: "包裹退回的相关费用",
  },
};

// 费用调整类型选项（初始化为标准类型，后续会从后端获取更新）
export let ADJUSTMENT_TYPE_OPTIONS: FinancialAdjustmentType[] = [
  STANDARD_ADJUSTMENT_TYPES.COMPENSATION,
  STANDARD_ADJUSTMENT_TYPES.REASSIGNMENT,
  STANDARD_ADJUSTMENT_TYPES.DESTRUCTION,
  STANDARD_ADJUSTMENT_TYPES.RETURN,
];

/**
 * 获取费用调整类型列表
 * @param keyword 搜索关键词
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 费用调整类型列表
 */
/**
 * 获取费用调整类型列表
 * @param keyword 搜索关键词
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 费用调整类型列表
 */
export const fetchAdjustmentTypes = async (
  keyword: string = "",
  page: number = 1,
  pageSize: number = 20
): Promise<FinancialAdjustmentType[]> => {
  try {
    // 调用API获取费用调整类型
    const response = await apiClient.get<FinancialAdjustmentTypesResponse>(
      getApiPath("/financial-adjustment-types"),
      { params: { keyword, page, pageSize } }
    );

    // 验证响应数据格式
    if (!response.data?.data?.list) {
      throw new Error("Invalid response format: missing required fields");
    }

    // 转换数据格式为下拉框需要的格式
    const formattedItems: FinancialAdjustmentType[] =
      response.data.data.list.map((item) => ({
        value: item.id.toString(),
        label: item.name,
        description: item.description,
      }));

    // 更新全局的调整类型选项
    ADJUSTMENT_TYPE_OPTIONS = formattedItems;

    return formattedItems;
  } catch (error) {
    console.error("Failed to fetch adjustment types:", error);
    return [];
  }
};

// 货币单位选项
export const CURRENCY_OPTIONS = [
  { value: "CNY", label: "人民币 (CNY)" },
  { value: "USD", label: "美元 (USD)" },
  { value: "EUR", label: "欧元 (EUR)" },
];

// 费用调整请求体接口
export interface AddFinancialAdjustmentRequest {
  manifestId: number; // 关联的运单ID (必填)
  adjustmentType: string; // 调整类型 (必填)
  description?: string; // 调整描述/原因 (选填)
  amount: number; // 调整金额 (必填，正数表示收入，负数表示支出/赔偿)
  currency: string; // 货币单位 (必填)
  effectiveDate: string; // 费用实际发生/确认日期 (必填)
  customerAccountId: number; // 客户ID (必填)
  attachmentPaths?: string; // 附件路径列表 (选填)
}

// 费用调整响应接口
export interface AddFinancialAdjustmentResponse {
  id: number; // 新创建的财务调整记录ID
}

// 作废费用调整请求体接口
export interface VoidFinancialAdjustmentRequest {
  id: number; // 财务调整记录ID (必填)
  voidReason: string; // 作废原因 (必填)
}

// 作废费用调整响应接口
export interface VoidFinancialAdjustmentResponse {
  success: boolean; // 操作是否成功
}

// 创建费用调整类型请求接口
export interface CreateAdjustmentTypeRequest {
  name: string;
  description?: string;
}

// 创建费用调整类型响应接口
export interface CreateAdjustmentTypeResponse {
  id: number;
  name: string;
  description: string;
}

/**
 * 创建费用调整类型
 * @param data 费用调整类型数据
 * @returns 创建的费用调整类型
 * @throws {Error} 当请求失败时抛出错误
 */
export const createAdjustmentType = async (
  data: CreateAdjustmentTypeRequest
): Promise<CreateAdjustmentTypeResponse> => {
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: CreateAdjustmentTypeResponse;
  }>(getApiPath("/financial-adjustment-types"), data);

  if (!response.data || !response.data.data) {
    throw new Error(
      "Failed to create adjustment type: Invalid response from server"
    );
  }

  // 刷新调整类型列表
  await fetchAdjustmentTypes();

  return response.data.data;
};

/**
 * 添加费用调整
 * @param data 费用调整数据
 * @returns 添加结果，包含新创建的调整记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addFinancialAdjustment = async (
  data: AddFinancialAdjustmentRequest
): Promise<AddFinancialAdjustmentResponse> => {
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments"), data);

  if (!response.data || !response.data.data) {
    throw new Error(
      "Failed to add financial adjustment: Invalid response from server"
    );
  }

  return response.data.data;
};

/**
 * 作废费用调整
 * @param data 作废请求数据，包含调整记录ID和作废原因
 * @returns 作废操作结果
 * @throws {Error} 当请求失败时抛出错误
 */
export const voidFinancialAdjustment = async (
  data: VoidFinancialAdjustmentRequest
): Promise<VoidFinancialAdjustmentResponse> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的success检查
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: VoidFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/void"), data);

  return response.data.data;
};

// 赔偿调整请求体接口 (Compensation Adjustment Payload Interface)
export interface CompensationAdjustmentPayload {
  manifestId: number; // 关联的运单ID (必填)
  customerAccountId: number; // 客户ID (必填)
  isFreightDeduction: boolean; // 是否扣运费 (必填)
  freightDeductionPercentage?: number; // 扣运费比例 (0-100, isFreightDeduction为true时可选)
  totalFreightDeductionAmount?: number; // 扣运费总额 (isFreightDeduction为true时可选)
  isValueCompensation: boolean; // 是否保价赔偿 (必填)
  cargoValue?: number; // 货值 (新增, isValueCompensation为true时必填)
  valueCompensationPercentage?: number; // 保价赔偿比例 (0-100, isValueCompensation为true时可选)
  totalValueCompensationAmount?: number; // 保价赔偿总额 (isValueCompensation为true时可选)
  totalCompensationAmount: number; // 总赔偿金额 (必填, 必须大于0)
  description: string; // 赔偿说明/备注 (必填)
  effectiveDate: string; // 生效日期 (必填, YYYY-MM-DD)
  currency: string; // 货币单位 (必填)
  proofOfValueImageUrls?: string[]; // 货值证明图片链接 (可选, 字符串数组)
}

/**
 * 添加赔偿调整
 * @param data 赔偿调整数据
 * @returns 添加结果，包含新创建的赔偿记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addCompensationAdjustment = async (
  data: CompensationAdjustmentPayload
): Promise<AddFinancialAdjustmentResponse> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的success检查
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/compensations"), data);

  return response.data.data;
};

// 改派调整请求体接口 (Reassignment Adjustment Payload Interface)
export interface ReassignmentAdjustmentPayload {
  manifestId: number; // 关联的运单ID
  reassignmentNumber: string; // 改派单号
  amount: number; // 改派费用
  description?: string; // 改派描述/原因（可选）
  effectiveDate: string; // 生效日期，格式: YYYY-MM-DD
  currency: string; // 货币代码，如: CNY, USD
  customerAccountId: number; // 客户账户ID
}

/**
 * 添加改派调整
 * @param data 改派调整数据
 * @returns 添加结果，包含新创建的改派记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addReassignmentAdjustment = async (
  data: ReassignmentAdjustmentPayload
): Promise<AddFinancialAdjustmentResponse> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的success检查
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/reassignments"), data);

  return response.data.data;
};

// 销毁调整请求体接口 (Destruction Adjustment Payload Interface)
export interface DestructionAdjustmentPayload {
  manifestId: number;
  customerAccountId: number;
  amount: number;
  description?: string;
  effectiveDate: string;
  currency: string;
}

/**
 * 添加销毁调整
 * @param data 销毁调整数据
 * @returns 添加结果，包含新创建的销毁记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addDestructionAdjustment = async (
  data: DestructionAdjustmentPayload
): Promise<AddFinancialAdjustmentResponse> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的success检查
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/destructions"), data);

  return response.data.data;
};

// 退回调整请求体接口 (Return Adjustment Payload Interface)
export interface ReturnAdjustmentPayload {
  manifestId: number;
  customerAccountId: number;
  amount: number;
  description?: string;
  effectiveDate: string;
  currency: string;
}

/**
 * 添加退回调整
 * @param data 退回调整数据
 * @returns 添加结果，包含新创建的退回记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addReturnAdjustment = async (
  data: ReturnAdjustmentPayload
): Promise<AddFinancialAdjustmentResponse> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的success检查
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/returns"), data);

  return response.data.data;
};
