/* 移除 Vite 默认样式 */
/* :root { ... } */
/* a { ... } */
/* a:hover { ... } */
/* body { ... } */ /* 移除默认 body 样式 */
/* h1 { ... } */
/* button { ... } */
/* button:hover { ... } */
/* button:focus, button:focus-visible { ... } */
/* @media (prefers-color-scheme: light) { ... } */

/* 基础样式重置和布局 */
html,
body,
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh; /* 使用 vh 确保占满视口高度 */
  /* 更新字体栈，优先使用 PingFang SC 和 Microsoft YaHei */
  font-family: "PingFang SC", "Microsoft YaHei", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  background-color: #fff; /* 默认背景设为白色，避免深色闪烁 */
  letter-spacing: 0.3px; /* 全局字间距优化 */
}

/* 表格斑马纹样式 */
.zebra-stripe {
  background-color: #f9fafb; /* 浅灰色背景 */
}

/* 鼠标悬停行效果 */
.ant-table-tbody > tr:hover > td {
  background-color: #edf2f7 !important; /* 保持一致的悬停效果 */
}

.login-page {
  width: 100%; /* 确保 Row 占满宽度 */
  min-height: 100vh; /* 确保占满整个视口高度 */
  display: flex; /* 使用 flex 布局，以便列能正常工作 */
  position: relative; /* 为装饰元素定位 */
  overflow: hidden; /* 避免装饰元素溢出 */
}

/* 移除整个底部遮罩 */
.login-page::after {
  display: none; /* 完全禁用这个元素 */
}

/* 左侧品牌区 - 更新占比为 60% */
.brand-section {
  /* display: flex; */ /* Row 已经是 flex 了，Col 不需要 */
  flex: 0 0 60%; /* 调整为 60% */
  display: flex; /* 改为 flex 以便内部元素居中 */
  flex-direction: column; /* 垂直排列 */
  align-items: center;
  justify-content: center;
  /* 优化背景渐变 - 更深蓝到浅蓝 */
  background-color: #4b91f1; /* 调整底色，稍微深一些 */
  background-image: linear-gradient(
    135deg,
    #0a1f38 0%,
    #1e3a8a 50%,
    transparent 100%
  ); /* 调整渐变，使用更深的蓝黑色起始 */
  color: #fff;
  padding: 40px;
  padding-bottom: 0; /* 移除底部内边距 */
  margin-bottom: 0; /* 确保没有底部边距 */
  text-align: center;
  position: relative; /* 用于伪元素 */
  position: relative;
  overflow: hidden; /* 防止动画效果溢出 */
  border-right: 1px solid rgba(255, 255, 255, 0.1); /* 添加右侧边框，增强分割感 */
}

/* 移除原先的波浪装饰，改用渐变遮罩 */
.brand-section::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px; /* 调整高度 */
  /* 从透明到当前背景色的渐变，确保底部为纯色 */
  background: linear-gradient(
    to bottom,
    rgba(75, 145, 241, 0) 0%,
    rgba(75, 145, 241, 1) 100%
  );
  z-index: 2;
  opacity: 0.8; /* 降低不透明度，让底色更明显 */
}

/* 添加实底覆盖底部，确保无缝 */
.brand-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("/background.png");
  background-size: cover; /* 调整为 cover 以填满整个区域 */
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.15; /* 稍微提高基础透明度 */
  z-index: 1;
  animation: pulseBackground 5s ease-in-out infinite,
    /* 透明度呼吸动画 */ brightnessChange 8s ease-in-out infinite alternate,
    /* 亮度变化动画 */ rotateBackground 60s linear infinite; /* 旋转动画 */
  filter: brightness(1); /* 初始亮度 */
}

/* 修改脉冲动画，增大透明度变化范围 */
@keyframes pulseBackground {
  0%,
  100% {
    opacity: 0.12; /* 较暗状态 */
  }
  50% {
    opacity: 0.28; /* 较亮状态，增大变化 */
  }
}

/* 添加亮度变化动画 */
@keyframes brightnessChange {
  0% {
    filter: brightness(0.85) contrast(1.1); /* 较暗 */
  }
  50% {
    filter: brightness(1.15) contrast(1); /* 较亮 */
  }
  100% {
    filter: brightness(0.85) contrast(1.1); /* 回到较暗 */
  }
}

/* 定义背景旋转动画 */
@keyframes rotateBackground {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.brand-content {
  position: relative; /* 确保内容在伪元素之上 */
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 500px; /* 限制内容宽度 */
}

.brand-logo {
  max-width: 150px; /* 调整 Logo 大小 */
  margin-bottom: 30px;
  /* 添加 Logo 渐变效果 */
  filter: drop-shadow(0 0 10px rgba(249, 115, 22, 0.3));
  transition: transform 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.05);
}

.brand-title {
  color: #fff !important; /* 覆盖 Ant Design 默认颜色 */
  margin-bottom: 10px !important;
  font-size: 2.5rem !important; /* 增大标题字体 */
  letter-spacing: 0.5px; /* 增加字间距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
}

.brand-subtitle {
  font-size: 1.4em;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 1px; /* 增加字间距 */
}

.brand-tagline {
  font-size: 1em;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
  position: relative;
  padding-top: 10px;
}

.brand-tagline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0)
  );
}

/* 右侧登录区 - 更新占比为 40% */
.login-section {
  flex: 0 0 50%; /* 调整为 40% */
  display: flex; /* 改为 flex 以便内部元素居中 */
  align-items: center;
  justify-content: center;
  background-color: #fff; /* 纯白背景 */
  padding: 40px 0; /* 移除水平内边距，保留垂直内边距 */
  position: relative; /* 为分隔线定位 */
}

/* 添加分隔线 */
.login-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom, transparent, #3b82f6, transparent);
}

.login-form-container {
  width: 100%;
  max-width: 450px; /* 从400px扩大到600px (1.5倍) */
  padding: 40px 35px; /* 调整内边距 */
  border-radius: 16px;
  /* 增强登录框阴影效果 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(0, 0, 0, 0.03);
  background-color: #fff;
  transition: all 0.3s ease;
}

.login-greeting {
  text-align: center;
  margin-bottom: 12px !important; /* 增加与下方文字的间距 */
  font-weight: 600;
  font-size: 1.8rem !important;
  color: #1e3a8a !important;
  letter-spacing: 0.5px;
}

.login-system-name {
  display: block;
  text-align: center;
  margin-bottom: 42px !important; /* 增加与输入框的间距 */
  color: #6b7280; /* 浅灰色 */
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.login-form {
  width: 100%;
}

/* 输入框基础样式 */
.login-form .ant-input-affix-wrapper {
  border-radius: 10px; /* 增加圆角 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  padding: 12px 14px; /* 增加内边距 */
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1); /* 使用更平滑的动画 */
  margin-bottom: 20px; /* 输入框之间的间距 */
}

/* 覆盖浏览器自动填充样式 */
.login-form .ant-input-affix-wrapper input:-webkit-autofill,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:hover,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:focus,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important; /* 使用白色背景填充 */
  box-shadow: 0 0 0 30px white inset !important;
  background-color: white !important; /* 确保背景色是白色 */
  -webkit-text-fill-color: inherit !important; /* 保持文字颜色 */
}

/* 输入框焦点动效 */
.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  transform: scale(1.02) translateY(-2px); /* 增加细微的上移效果 */
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.15); /* 增强阴影效果 */
  border-color: #3b82f6;
}

/* 输入框悬停效果 */
.login-form .ant-input-affix-wrapper:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.1);
}

/* 输入框内图标样式 */
.login-form .ant-input-prefix {
  color: #3b82f6;
  margin-right: 12px; /* 增加图标与文本间距 */
  font-size: 18px; /* 增大图标尺寸 */
  display: flex; /* 确保图标垂直居中 */
  align-items: center;
}

/* 记住登录信息行样式 */
.login-form .ant-row {
  margin-bottom: 25px; /* 增加与按钮的间距 */
}

.login-form-forgot {
  float: right;
  color: #3b82f6 !important;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500; /* 加粗一点 */
}

.login-form-forgot:hover {
  color: #1e3a8a !important;
  text-decoration: underline;
}

/* 自定义复选框样式 */
.login-form .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.login-form .ant-checkbox-inner {
  border-radius: 4px;
  border-width: 1.5px; /* 加粗边框 */
  transition: all 0.3s ease;
}

.login-form .ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #3b82f6;
}

.login-form-button {
  width: 100%;
  height: 50px; /* 增加按钮高度 */
  font-size: 1.05rem; /* 增大文本 */
  font-weight: 500;
  letter-spacing: 1.2px; /* 增加字间距 */
  /* 调整为更具层次感的蓝色渐变 */
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #1e40af) !important;
  border: none !important;
  border-radius: 12px !important; /* 增加圆角 */
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1) !important; /* 使用更丰富的动画曲线 */
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.25),
    0 0 0 3px rgba(59, 130, 246, 0.05) !important;
  position: relative;
  overflow: hidden;
}

.login-form-button:hover {
  transform: translateY(-3px) scale(1.015); /* 上移更明显 */
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.35),
    0 0 0 3px rgba(59, 130, 246, 0.08) !important;
  background: linear-gradient(
    135deg,
    #3b82f6,
    #1e40af,
    #1e3a8a
  ) !important; /* 悬停时加深渐变 */
}

.login-form-button:active {
  transform: translateY(1px) scale(0.99); /* 按下时缩小效果 */
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
}

/* 版权区域与表单的分隔 */
.login-footer {
  position: relative;
  margin-top: 40px;
  padding-top: 20px; /* 增加上部内边距 */
  text-align: center;
  font-size: 12px;
  color: #9ca3af;
  /* 添加分隔线 */
  border-top: 1px solid #f3f4f6;
}

.login-footer::before {
  content: "";
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
}

.footer-text {
  display: block;
  margin-bottom: 6px;
  font-size: 11px; /* 稍微小一点 */
}

/* 隐藏社交图标 */
.social-icons {
  display: none;
}

/* 响应式布局调整 */
@media (max-width: 767px) {
  .login-page {
    flex-direction: column; /* 小屏幕时改为垂直排列 */
  }
  .brand-section {
    /* display: none; */ /* 不隐藏，让它在上面 */
    flex: 0 0 auto; /* 高度自适应 */
    min-height: 250px; /* 给个最小高度 */
    width: 100%; /* 宽度占满 */
  }
  .login-section {
    flex: 1 1 auto; /* 占据剩余空间 */
    width: 100%; /* 宽度占满 */
    padding: 20px; /* 调整内边距 */
  }
  .login-section::before {
    display: none; /* 移除分隔线 */
  }
  .login-form-container {
    box-shadow: none; /* 移除阴影 */
    padding: 20px;
  }
  .brand-title {
    font-size: 1.8rem !important;
  }
  .brand-subtitle {
    font-size: 1.1em;
  }
}
