/* 左侧品牌区 - 更新占比为 60% */
.brand-section {
  /* display: flex; */ /* Row 已经是 flex 了，Col 不需要 */
  flex: 0 0 60%; /* 调整为 60% */
  display: flex; /* 改为 flex 以便内部元素居中 */
  flex-direction: column; /* 垂直排列 */
  align-items: center;
  justify-content: center;
  /* 优化背景渐变 - 更深蓝到浅蓝 */
  background-color: #4b91f1; /* 调整底色，稍微深一些 */
  background-image: linear-gradient(
    135deg,
    #0a1f38 0%,
    #1e3a8a 50%,
    transparent 100%
  ); /* 调整渐变，使用更深的蓝黑色起始 */
  color: #fff;
  padding: 40px;
  padding-bottom: 0; /* 移除底部内边距 */
  margin-bottom: 0; /* 确保没有底部边距 */
  text-align: center;
  position: relative; /* 用于伪元素 */
  position: relative;
  overflow: hidden; /* 防止动画效果溢出 */
  border-right: 1px solid rgba(255, 255, 255, 0.1); /* 添加右侧边框，增强分割感 */
}

/* 移除原先的波浪装饰，改用渐变遮罩 */
.brand-section::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150px; /* 调整高度 */
  /* 从透明到当前背景色的渐变，确保底部为纯色 */
  background: linear-gradient(
    to bottom,
    rgba(75, 145, 241, 0) 0%,
    rgba(75, 145, 241, 1) 100%
  );
  z-index: 2;
  opacity: 0.8; /* 降低不透明度，让底色更明显 */
}

/* 添加实底覆盖底部，确保无缝 */
.brand-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("/background.png");
  background-size: cover; /* 调整为 cover 以填满整个区域 */
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.15; /* 稍微提高基础透明度 */
  z-index: 1;
  animation: pulseBackground 5s ease-in-out infinite,
    /* 透明度呼吸动画 */ brightnessChange 8s ease-in-out infinite alternate,
    /* 亮度变化动画 */ rotateBackground 60s linear infinite; /* 旋转动画 */
  filter: brightness(1); /* 初始亮度 */
}

/* 修改脉冲动画，增大透明度变化范围 */
@keyframes pulseBackground {
  0%,
  100% {
    opacity: 0.12; /* 较暗状态 */
  }
  50% {
    opacity: 0.28; /* 较亮状态，增大变化 */
  }
}

/* 添加亮度变化动画 */
@keyframes brightnessChange {
  0% {
    filter: brightness(0.85) contrast(1.1); /* 较暗 */
  }
  50% {
    filter: brightness(1.15) contrast(1); /* 较亮 */
  }
  100% {
    filter: brightness(0.85) contrast(1.1); /* 回到较暗 */
  }
}

/* 定义背景旋转动画 */
@keyframes rotateBackground {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.brand-content {
  position: relative; /* 确保内容在伪元素之上 */
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 500px; /* 限制内容宽度 */
}

.brand-logo {
  max-width: 150px; /* 调整 Logo 大小 */
  margin-bottom: 30px;
  /* 添加 Logo 渐变效果 */
  filter: drop-shadow(0 0 10px rgba(249, 115, 22, 0.3));
  transition: transform 0.3s ease;
}

.brand-logo:hover {
  transform: scale(1.05);
}

.brand-title {
  color: #fff !important; /* 覆盖 Ant Design 默认颜色 */
  margin-bottom: 10px !important;
  font-size: 2.5rem !important; /* 增大标题字体 */
  letter-spacing: 0.5px; /* 增加字间距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
}

.brand-subtitle {
  font-size: 1.4em;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 1px; /* 增加字间距 */
}

.brand-tagline {
  font-size: 1em;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.5px;
  position: relative;
  padding-top: 10px;
}

.brand-tagline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0)
  );
}
