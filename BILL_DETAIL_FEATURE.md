# 账单详情功能

## 功能概述

新增了账单记录详情功能，包含账单基本信息展示和明细分页查询。用户可以通过账单管理页面的"详情"按钮查看某条账单的完整信息。

## 功能特性

### 🔍 **账单基本信息**

- **统计信息**: 账单总金额、已付金额、应付余额、明细数量
- **基本信息**: 账单编号、客户信息、日期信息、支付信息等
- **运费模板**: 展示生成账单时使用的三种运费模板配置（普通货物、带电货物、投函货物）
- **状态展示**: 可视化显示账单状态（未付款/已付款/部分付款/逾期/作废）

### 📊 **账单明细列表**

- **分页查询**: 支持分页显示账单明细记录
- **详细信息**: 运单信息、收件人、物品描述、货物类型、重量尺寸、费用明细、时间信息
- **智能显示**: 自动处理空值，友好的数据展示

### 🎨 **UI/UX 特性**

- **响应式设计**: 适配不同屏幕尺寸
- **Loading 状态**: 数据加载时的友好提示
- **错误处理**: 完善的错误提示和处理
- **导航支持**: 返回列表、刷新数据等操作

## 技术实现

### **API 接口**

1. **获取账单详情**

   ```typescript
   GET / api / v1 / finance / billing / records / { billingRecordId };
   ```

2. **获取账单明细**
   ```typescript
   GET /api/v1/finance/billing/records/{billingRecordId}/items?page=1&pageSize=20
   ```

### **路由配置**

```typescript
// App.tsx
<Route path="billing-finance">
  <Route index element={<BillManagementPage />} />
  <Route path="detail/:id" element={<BillDetailPage />} />
</Route>
```

### **核心组件**

- `BillDetailPage.tsx` - 账单详情页面主组件
- `BillingService.ts` - 新增 API 方法和类型定义

## 使用方法

### **进入详情页**

1. 打开财务管理 → 账单管理页面
2. 在账单列表中点击任意一条记录的"详情"按钮
3. 系统会跳转到账单详情页面

### **详情页操作**

- **返回列表**: 点击"返回列表"按钮回到账单管理页面
- **刷新数据**: 点击"刷新数据"按钮重新加载账单信息和明细
- **分页浏览**: 在明细表格中使用分页控件浏览更多明细

## 数据结构

### **账单详情 (BillingRecordDetail)**

```typescript
interface BillingRecordDetail {
  id: number;
  billNumber: string;
  customerAccountId: number;
  customerNickname: string;
  customerUsername: string;
  billDate: string;
  dueDate?: string;
  billingPeriodStart: string;
  billingPeriodEnd: string;
  appliedFreightTemplates: {
    generalTemplate?: ShippingFeeTemplate;
    batteryTemplate?: ShippingFeeTemplate;
    postBoxTemplate?: ShippingFeeTemplate;
  };
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  currency: string;
  status: string;
  statusName: string;
  // ... 其他字段
}
```

### **账单明细 (BillingRecordItem)**

```typescript
interface BillingRecordItem {
  id: number;
  billingRecordId: number;
  manifestId?: number;
  expressNumber?: string;
  orderNo?: string;
  transferredTrackingNumber?: string;
  orderNumber?: string;
  // ... 时间字段
  receiverName?: string;
  itemDescription: string;
  cargoType: number;
  cargoTypeName: string;
  // ... 重量尺寸字段
  // ... 费用字段
  itemTotalAmount: number;
  createTime: string;
  updateTime: string;
}
```

## 视觉设计

### **页面布局**

- **顶部**: 导航按钮（返回列表、刷新数据）
- **上半部分**: 账单基本信息卡片
  - 统计数据区域（4 个关键指标）
  - 详细信息表格
  - 运费模板配置展示
- **下半部分**: 账单明细表格
  - 支持横向滚动
  - 分页控件
  - 空状态处理

### **状态标识**

- **未付款**: 橙色标签
- **已付款**: 绿色标签
- **部分付款**: 蓝色标签
- **逾期**: 红色标签
- **作废**: 灰色标签

### **货物类型**

- **普通货物**: 蓝色标签
- **带电货物**: 橙色标签
- **投函货物**: 绿色标签

## 错误处理

- **无效 ID**: 显示错误提示，提供返回按钮
- **网络错误**: Toast 提示，支持重试
- **数据不存在**: 空状态页面，引导用户返回
- **权限不足**: 401/403 错误处理

## 性能优化

- **分页查询**: 避免一次性加载大量明细数据
- **状态管理**: 合理的 loading 状态控制
- **组件拆分**: 模块化的 UI 组件设计
- **错误边界**: 防止组件崩溃影响整体应用

## 后续扩展

- **打印功能**: 支持账单详情和明细的打印导出
- **编辑功能**: 支持账单信息的修改（需权限控制）
- **支付功能**: 集成支付接口，支持在线支付
- **审计日志**: 记录账单的查看和操作历史

这个功能完善了账单管理模块的核心功能，为用户提供了完整的账单查看和管理体验。
