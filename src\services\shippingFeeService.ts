import { ApiResponse, apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 货物类型接口
export interface ShipmentType {
  id: number;
  code: string;
  name: string;
  isActive: boolean;
}

// 运费模板接口
export interface ShippingFeeTemplate {
  id: number;
  name: string;
  type: number;
  typeName: string;
  firstWeightPrice: number;
  firstWeightRange: number;
  continuedWeightPrice: number;
  continuedWeightInterval: number;
  bulkCoefficient: number;
  threeSidesStart: number;
  createTime: string;
  updateTime: string;
}

// 获取启用的货物类型列表响应
export interface ShipmentTypesResponse {
  list: ShipmentType[];
}

// 根据类型获取运费模板列表响应
export interface ShippingFeeTemplatesResponse {
  type: number;
  typeName: string;
  templates: ShippingFeeTemplate[];
}

/**
 * 获取启用的货物类型列表
 */
export async function getActiveShipmentTypes(): Promise<ShipmentType[]> {
  try {
    const response = await apiClient.get<ApiResponse<ShipmentTypesResponse>>(
      getApiPath("/shipment-types/active")
    );

    return response.data.data?.list || [];
  } catch (error) {
    console.error("获取货物类型失败:", error);
    throw error;
  }
}

/**
 * 根据类型获取运费模板列表
 */
export async function getShippingFeeTemplatesByType(
  type: number
): Promise<ShippingFeeTemplatesResponse> {
  try {
    const response = await apiClient.get<
      ApiResponse<ShippingFeeTemplatesResponse>
    >(getApiPath(`/shipping-fee-templates/type/${type}`));

    if (!response.data.data) {
      throw new Error("获取运费模板数据失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("获取运费模板失败:", error);
    throw error;
  }
}

/**
 * 根据货物类型获取对应的模板类型
 * @param shipmentType 货物类型
 * @returns 模板类型ID
 */
export function getTemplateTypeFromShipmentType(
  shipmentType: ShipmentType
): number {
  // 根据货物类型的名称或代码来映射模板类型
  const typeName = shipmentType.name.toLowerCase();
  const typeCode = shipmentType.code.toLowerCase();

  // 带电货物 -> 带电模板 (类型2)
  if (
    typeName.includes("带电") ||
    typeCode.includes("battery") ||
    typeCode.includes("electric")
  ) {
    return 2;
  }

  // 投函货物 -> 投函模板 (类型3)
  if (
    typeName.includes("投函") ||
    typeCode.includes("post") ||
    typeCode.includes("box")
  ) {
    return 3;
  }

  // 特殊货物 -> 特殊模板 (类型6)
  if (typeName.includes("特殊") || typeCode.includes("special")) {
    return 6;
  }

  // 默认为普通模板 (类型1)
  return 1;
}

// 创建运费模板请求参数接口
export interface CreateShippingFeeTemplateRequest {
  name: string;
  firstWeightPrice: number;
  firstWeightRange: number;
  continuedWeightPrice: number;
  continuedWeightInterval: number;
  bulkCoefficient: number;
  threeSidesStart: number;
  type: number;
}

/**
 * 创建新的运费模板
 */
export async function createShippingFeeTemplate(
  data: CreateShippingFeeTemplateRequest
): Promise<ShippingFeeTemplate> {
  try {
    const response = await apiClient.post<ApiResponse<ShippingFeeTemplate>>(
      getApiPath("/shipping-fee-templates"),
      data
    );

    if (!response.data.data) {
      throw new Error("创建运费模板失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("创建运费模板失败:", error);
    throw error;
  }
}

// 更新运费模板请求参数接口
export interface UpdateShippingFeeTemplateRequest {
  id: number;
  name: string;
  firstWeightPrice: number;
  firstWeightRange: number;
  continuedWeightPrice: number;
  continuedWeightInterval: number;
  bulkCoefficient: number;
  threeSidesStart: number;
  type: number;
}

/**
 * 更新现有的运费模板
 */
export async function updateShippingFeeTemplate(
  data: UpdateShippingFeeTemplateRequest
): Promise<ShippingFeeTemplate> {
  try {
    const response = await apiClient.put<ApiResponse<ShippingFeeTemplate>>(
      getApiPath("/shipping-fee-templates"),
      data
    );

    if (!response.data.data) {
      throw new Error("更新运费模板失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("更新运费模板失败:", error);
    throw error;
  }
}

/**
 * 删除运费模板
 */
export async function deleteShippingFeeTemplate(id: number): Promise<void> {
  try {
    await apiClient.delete<ApiResponse<null>>(
      getApiPath(`/shipping-fee-templates/${id}`)
    );
  } catch (error) {
    console.error("删除运费模板失败:", error);
    throw error;
  }
}

// 用户模板配置相关接口

// 用户模板配置项
export interface UserTemplateConfiguration {
  type: number;
  templateId: number;
}

// 获取用户模板配置响应
export interface UserTemplateConfigResponse {
  userId: number;
  templates: ShippingFeeTemplate[];
}

// 批量更新用户模板配置请求
export interface UpdateUserTemplateConfigRequest {
  configurations: UserTemplateConfiguration[];
}

/**
 * 获取用户的运费模板配置
 */
export async function getUserTemplateConfigurations(
  userId: number
): Promise<UserTemplateConfigResponse> {
  try {
    const response = await apiClient.get<
      ApiResponse<UserTemplateConfigResponse>
    >(getApiPath(`/shipping-fee-templates/user/${userId}`));

    if (!response.data.data) {
      throw new Error("获取用户模板配置失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("获取用户模板配置失败:", error);
    throw error;
  }
}

/**
 * 批量更新用户模板配置
 */
export async function updateUserTemplateConfigurations(
  userId: number,
  data: UpdateUserTemplateConfigRequest
): Promise<UserTemplateConfigResponse> {
  try {
    const response = await apiClient.put<
      ApiResponse<UserTemplateConfigResponse>
    >(
      getApiPath(`/shipping-fee-templates/user/${userId}/configurations`),
      data
    );

    if (!response.data.data) {
      throw new Error("更新用户模板配置失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("更新用户模板配置失败:", error);
    throw error;
  }
}
