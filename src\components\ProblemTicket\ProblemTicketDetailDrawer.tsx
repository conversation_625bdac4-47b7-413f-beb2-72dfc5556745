import React, { useEffect, useState } from "react";
import {
  Drawer,
  Descriptions,
  Table,
  Spin,
  Alert,
  Tag,
  Empty,
  Timeline,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import {
  getManifestDetailsByManifestId,
  ManifestDetailData,
  ManifestItem,
  getManifestTrackings,
  TrackingEvent,
} from "../../services/problemTicketService";

// 定义错误对象的通用接口，包含更完整的响应结构
interface ApiError {
  message: string;
  response?: {
    status?: number;
    statusText?: string;
    data?: {
      errorMessage?: string;
      errorCode?: number;
    };
  };
}

interface ProblemTicketDetailDrawerProps {
  manifestId: number | null;
  visible: boolean;
  onClose: () => void;
}

const ProblemTicketDetailDrawer: React.FC<ProblemTicketDetailDrawerProps> = ({
  manifestId,
  visible,
  onClose,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [manifestDetail, setManifestDetail] =
    useState<ManifestDetailData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [trackingsLoading, setTrackingsLoading] = useState<boolean>(false);
  const [trackings, setTrackings] = useState<TrackingEvent[]>([]);
  const [trackingsError, setTrackingsError] = useState<string | null>(null);

  useEffect(() => {
    if (visible && manifestId !== null) {
      setLoading(true);
      setError(null);
      setManifestDetail(null);
      getManifestDetailsByManifestId(manifestId)
        .then((data) => {
          setManifestDetail(data);
        })
        .catch((err) => {
          console.error("获取清单详情失败 - 原始错误对象:", err);

          const apiError = err as ApiError;
          let displayMessage = "获取清单详情失败，请稍后重试。"; // Default fallback
          const statusCode = apiError.response?.status;
          const backendErrorMessage = apiError.response?.data?.errorMessage;
          const genericMessage = apiError.message;

          if (statusCode && statusCode >= 500 && statusCode <= 599) {
            // 服务器内部错误
            displayMessage =
              backendErrorMessage ||
              `服务器内部错误 (状态码: ${statusCode})，请稍后重试。`;
          } else if (backendErrorMessage) {
            // 客户端错误或其他错误，但后端提供了 errorMessage
            displayMessage = backendErrorMessage;
          } else if (genericMessage) {
            // 没有后端 errorMessage，使用 Axios 或网络层面错误信息
            displayMessage = genericMessage;
          }

          setError(displayMessage);
        })
        .finally(() => {
          setLoading(false);
        });

      setTrackingsLoading(true);
      setTrackingsError(null);
      setTrackings([]);
      getManifestTrackings(manifestId)
        .then((trackingData) => {
          setTrackings(trackingData);
        })
        .catch((err) => {
          console.error("获取物流轨迹失败 - 原始错误对象:", err);
          const apiError = err as ApiError;
          let displayMessage = "获取物流轨迹失败，请稍后重试。";
          const statusCode = apiError.response?.status;
          const backendErrorMessage = apiError.response?.data?.errorMessage;
          const genericMessage = apiError.message;
          if (statusCode && statusCode >= 500 && statusCode <= 599) {
            displayMessage =
              backendErrorMessage ||
              `获取物流轨迹时发生服务器错误 (状态码: ${statusCode})。`;
          } else if (backendErrorMessage) {
            displayMessage = backendErrorMessage;
          } else if (genericMessage) {
            displayMessage = genericMessage;
          }
          setTrackingsError(displayMessage);
        })
        .finally(() => {
          setTrackingsLoading(false);
        });
    } else if (!visible) {
      setManifestDetail(null);
      setError(null);
      setLoading(false);
      setTrackings([]);
      setTrackingsError(null);
      setTrackingsLoading(false);
    }
  }, [visible, manifestId]);

  const itemColumns: ColumnsType<ManifestItem> = [
    { title: "物品名称", dataIndex: "name", key: "name" },
    { title: "数量", dataIndex: "quantity", key: "quantity", align: "right" },
    {
      title: "单价",
      dataIndex: "price",
      key: "price",
      align: "right",
      render: (val) => `¥${val.toFixed(2)}`,
    },
    {
      title: "总价值",
      dataIndex: "value",
      key: "value",
      align: "right",
      render: (val) => `¥${val.toFixed(2)}`,
    },
    { title: "重量 (kg)", dataIndex: "weight", key: "weight", align: "right" },
  ];

  return (
    <Drawer
      title={`运单详情 (ID: ${manifestId || "N/A"})`}
      placement="right"
      width={720}
      onClose={onClose}
      visible={visible}
      destroyOnClose
    >
      {loading && (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <Spin size="large" tip="加载详情中..." />
        </div>
      )}
      {error && (
        <Alert
          message="加载清单详情错误"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
        />
      )}
      {!loading && !error && !manifestDetail && visible && (
        <Empty
          description="暂无清单详情数据或未选择清单"
          style={{ marginTop: "20px" }}
        />
      )}
      {manifestDetail && (
        <>
          <Descriptions title="基础信息" bordered column={2} size="small">
            <Descriptions.Item label="清单ID">
              {manifestDetail.id}
            </Descriptions.Item>
            <Descriptions.Item label="快递单号">
              {manifestDetail.expressNumber}
            </Descriptions.Item>
            <Descriptions.Item label="订单号">
              {manifestDetail.orderNumber}
            </Descriptions.Item>
            <Descriptions.Item label="创建用户">
              {manifestDetail.userNickname} (ID: {manifestDetail.userId})
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {manifestDetail.createTime}
            </Descriptions.Item>
            <Descriptions.Item label="预报批次ID">
              {manifestDetail.preRegistrationBatchId}
            </Descriptions.Item>
            <Descriptions.Item label="校验状态" span={2}>
              <Tag
                color={manifestDetail.validateStatus === 0 ? "green" : "red"}
              >
                {manifestDetail.validateStatus === 0 ? "校验通过" : "校验失败"}
              </Tag>
            </Descriptions.Item>
            {manifestDetail.validateStatus !== 0 && (
              <Descriptions.Item label="校验错误信息" span={2}>
                {manifestDetail.validateError}
              </Descriptions.Item>
            )}
          </Descriptions>

          <Descriptions
            title="收件人信息"
            bordered
            column={2}
            size="small"
            style={{ marginTop: 24 }}
          >
            <Descriptions.Item label="收件人姓名">
              {manifestDetail.receiverName}
            </Descriptions.Item>
            <Descriptions.Item label="电话号码">
              {manifestDetail.receiverPhone}
            </Descriptions.Item>
            <Descriptions.Item label="邮编">
              {manifestDetail.receiverZipCode}
            </Descriptions.Item>
            <Descriptions.Item label="收件地址" span={2}>
              {manifestDetail.receiverAddress}
            </Descriptions.Item>
          </Descriptions>

          <h3 style={{ marginTop: 24, marginBottom: 16 }}>物品列表</h3>
          <Table
            columns={itemColumns}
            dataSource={manifestDetail.items}
            rowKey="name"
            pagination={false}
            bordered
            size="small"
            style={{ marginBottom: 24 }}
          />

          <h3 style={{ marginTop: 24, marginBottom: 16 }}>物流轨迹</h3>
          {trackingsLoading && (
            <div style={{ textAlign: "center", padding: "20px" }}>
              <Spin tip="加载物流轨迹中..." />
            </div>
          )}
          {trackingsError && (
            <Alert
              message="加载物流轨迹错误"
              description={trackingsError}
              type="error"
              showIcon
              closable
              onClose={() => setTrackingsError(null)}
              style={{ marginBottom: 16 }}
            />
          )}
          {!trackingsLoading && !trackingsError && trackings.length === 0 && (
            <Empty description="暂无物流轨迹信息" />
          )}
          {!trackingsLoading && !trackingsError && trackings.length > 0 && (
            <Timeline>
              {trackings.map((event) => (
                <Timeline.Item
                  key={event.id}
                  color={
                    event.status === -1
                      ? "red"
                      : event.status >= 8
                      ? "green"
                      : "blue"
                  }
                >
                  <p>
                    <strong>{event.track}</strong>
                  </p>
                  <p style={{ fontSize: "0.85em", color: "#888" }}>
                    时间: {event.time || event.createTime}
                    {event.place && ` | 地点: ${event.place}`}
                  </p>
                </Timeline.Item>
              ))}
            </Timeline>
          )}
        </>
      )}
    </Drawer>
  );
};

export default ProblemTicketDetailDrawer;
