import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  Form,
  Input,
  Row,
  Col,
  Select,
  DatePicker,
  message,
  Badge,
  Tooltip,
  Modal,
  Alert,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  FormOutlined,
  CheckCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import type { TableColumnsType, TablePaginationConfig } from "antd";
import {
  getProblemTickets,
  ProblemTicket,
  ProblemTicketQueryParams,
  PRIORITY_MAP,
  resolveProblemTicket,
  ResolveTicketPayload,
  updateProblemTicketRemark,
  resolveAllProblemTickets,
  ResolveAllTicketsPayload,
  resolveSpecificProblemTickets,
  ResolveSpecificTicketsPayload,
  markSpecificTicketsAsPending,
  MarkPendingTicketsPayload,
  getUsersWithProblems,
  UserWithProblems,
} from "../../services/problemTicketService";
import ProblemTicketDetailDrawer from "../../components/ProblemTicket/ProblemTicketDetailDrawer";

const { RangePicker } = DatePicker;
const { TextArea } = Input;

// 定义搜索表单值的类型
interface SearchFormValues {
  trackingNumber?: string;
  status?: string;
  problemTypeCode?: string;
  dateRange?: {
    format: (formatString: string) => string;
  }[];
  customerId?: number;
  [key: string]: unknown;
}

// 简化的状态选项
const SIMPLIFIED_STATUS_OPTIONS = [
  { value: "", label: "全部状态" },
  { value: "PENDING", label: "待处理" },
  { value: "RESOLVED", label: "已处理" },
];

// 新增：问题类型筛选选项
const PROBLEM_TYPE_FILTER_OPTIONS = [
  { label: "全部问题类型", value: "" },
  { label: "请咨询营业所", value: "BRANCH_INQUIRY" },
  { label: "未妥投退回", value: "DELIVERY_FAILED" },
];

// 定义错误对象的通用接口，包含更完整的响应结构
interface ApiError {
  message: string;
  response?: {
    status?: number;
    statusText?: string;
    data?: {
      errorMessage?: string;
      errorCode?: number;
    };
  };
}

const ProblemTicketPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<ProblemTicket[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchForm] = Form.useForm();
  const [resolveForm] = Form.useForm();

  // 新增：控制详情抽屉的状态
  const [detailDrawerVisible, setDetailDrawerVisible] =
    useState<boolean>(false);
  const [selectedManifestId, setSelectedManifestId] = useState<number | null>(
    null
  );

  // 新增：处理弹窗相关状态
  const [resolveModalVisible, setResolveModalVisible] =
    useState<boolean>(false);
  const [resolvingTicket, setResolvingTicket] = useState<ProblemTicket | null>(
    null
  );
  const [isResolving, setIsResolving] = useState<boolean>(false);

  // 新增: 处理错误状态
  const [resolveError, setResolveError] = useState<string | null>(null);

  // 新增: 修改备注相关状态
  const [remarkModalVisible, setRemarkModalVisible] = useState<boolean>(false);
  const [editingTicket, setEditingTicket] = useState<ProblemTicket | null>(
    null
  );
  const [isUpdatingRemark, setIsUpdatingRemark] = useState<boolean>(false);
  const [remarkForm] = Form.useForm();
  const [remarkError, setRemarkError] = useState<string | null>(null);

  // 新增：选择功能相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 新增: 批量处理相关状态
  const [resolveAllModalVisible, setResolveAllModalVisible] =
    useState<boolean>(false);
  const [isResolvingAll, setIsResolvingAll] = useState<boolean>(false);
  const [resolveAllForm] = Form.useForm();
  const [resolveAllError, setResolveAllError] = useState<string | null>(null);

  // 新增：批量处理结果弹窗相关状态
  const [batchActionResultModalVisible, setBatchActionResultModalVisible] =
    useState<boolean>(false);
  const [processedTicketsCount, setProcessedTicketsCount] = useState<
    number | null
  >(null);
  const [currentBatchActionType, setCurrentBatchActionType] = useState<
    "all" | "specific_resolved" | "specific_pending" | ""
  >("");

  // 新增：处理选中订单相关状态
  const [resolveSpecificModalVisible, setResolveSpecificModalVisible] =
    useState<boolean>(false);
  const [isResolvingSpecific, setIsResolvingSpecific] =
    useState<boolean>(false);
  const [resolveSpecificForm] = Form.useForm();
  const [resolveSpecificError, setResolveSpecificError] = useState<
    string | null
  >(null);

  // 新增：批量标记选中为待处理
  const [markPendingModalVisible, setMarkPendingModalVisible] =
    useState<boolean>(false);
  const [isMarkingPending, setIsMarkingPending] = useState<boolean>(false);
  const [markPendingForm] = Form.useForm();
  const [markPendingError, setMarkPendingError] = useState<string | null>(null);

  // 新增：用户问题订单弹窗相关状态
  const [usersModalVisible, setUsersModalVisible] = useState<boolean>(false);
  const [usersLoading, setUsersLoading] = useState<boolean>(false);
  const [usersWithProblems, setUsersWithProblems] = useState<
    UserWithProblems[]
  >([]);
  const [usersTotal, setUsersTotal] = useState<number>(0);
  const [usersCurrent, setUsersCurrent] = useState<number>(1);
  const [usersPageSize, setUsersPageSize] = useState<number>(10);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUserNickname, setSelectedUserNickname] = useState<string>("");

  // 加载数据
  const loadData = async (params: ProblemTicketQueryParams = {}) => {
    try {
      setLoading(true);
      const queryParams: ProblemTicketQueryParams = {
        page: params.page || current,
        pageSize: params.pageSize || pageSize,
        status: params.status === "" ? undefined : params.status,
        trackingNumber: params.trackingNumber || undefined,
        problemTypeCode:
          params.problemTypeCode === "" ? undefined : params.problemTypeCode,
        startTime: params.startTime || undefined,
        endTime: params.endTime || undefined,
        customerId: params.customerId || undefined,
      };
      Object.keys(queryParams).forEach(
        (key) =>
          queryParams[key as keyof ProblemTicketQueryParams] === undefined &&
          delete queryParams[key as keyof ProblemTicketQueryParams]
      );

      const result = await getProblemTickets(queryParams);
      setData(result.data || []);
      setTotal(result.total || 0);
    } catch (error) {
      const apiError = error as ApiError;
      console.error("加载问题订单失败 - 原始错误对象:", error);
      try {
        console.log(
          "加载问题订单失败 - 错误对象 JSON:",
          JSON.stringify(error, null, 2)
        );
      } catch (stringifyError) {
        console.log("加载问题订单失败 - 无法序列化错误对象:", stringifyError);
      }

      let displayMessage = "加载问题订单列表失败";
      const statusCode = apiError.response?.status;
      const backendErrorMessage = apiError.response?.data?.errorMessage;
      const genericMessage = apiError.message;

      console.log("Status Code:", statusCode);
      console.log(
        "Backend error message (from response.data.errorMessage):",
        backendErrorMessage
      );
      console.log(
        "Generic error message (from error.message):",
        genericMessage
      );

      if (statusCode && statusCode >= 500 && statusCode <= 599) {
        displayMessage =
          backendErrorMessage ||
          `服务器内部错误 (状态码: ${statusCode})，请稍后重试。`;
      } else if (backendErrorMessage) {
        displayMessage = backendErrorMessage;
      } else if (genericMessage) {
        displayMessage = genericMessage;
      }

      message.error(displayMessage);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和分页/排序变化时加载数据
  useEffect(() => {
    searchForm.setFieldsValue({ status: "PENDING", problemTypeCode: "" });
    loadData({ page: current, pageSize, status: "PENDING" });
  }, []);

  // 处理分页变化
  useEffect(() => {
    const currentFilters = searchForm.getFieldsValue();
    loadData({
      page: current,
      pageSize,
      status: currentFilters.status,
      problemTypeCode: currentFilters.problemTypeCode,
      trackingNumber: currentFilters.trackingNumber,
      customerId: currentFilters.customerId,
      startTime: currentFilters.dateRange?.[0]?.format("YYYY-MM-DD HH:mm:ss"),
      endTime: currentFilters.dateRange?.[1]?.format("YYYY-MM-DD HH:mm:ss"),
    });
  }, [current, pageSize]);

  // 表格分页变化处理
  const handleTableChange = (pagination: TablePaginationConfig) => {
    if (pagination.current) {
      setCurrent(pagination.current);
    }
    if (pagination.pageSize) {
      setPageSize(pagination.pageSize);
    }
  };

  // 搜索处理
  const handleSearch = (values: SearchFormValues) => {
    const { dateRange, ...rest } = values;
    const params: ProblemTicketQueryParams = {
      ...rest,
      page: 1,
    };
    if (dateRange && dateRange.length === 2) {
      params.startTime = dateRange[0].format("YYYY-MM-DD HH:mm:ss");
      params.endTime = dateRange[1].format("YYYY-MM-DD HH:mm:ss");
    }
    setCurrent(1);
    loadData(params);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    searchForm.setFieldsValue({ status: "PENDING", problemTypeCode: "" });
    setSelectedUserId(null);
    setSelectedUserNickname("");
    setCurrent(1);
    loadData({ page: 1, pageSize, status: "PENDING" });
  };

  // 修改：查看详情处理
  const handleViewDetail = (manifestId: number) => {
    setSelectedManifestId(manifestId);
    setDetailDrawerVisible(true);
  };

  const closeDetailDrawer = () => {
    setDetailDrawerVisible(false);
    setSelectedManifestId(null);
  };

  // 打开处理弹窗
  const handleOpenResolveModal = (ticket: ProblemTicket) => {
    setResolvingTicket(ticket);
    resolveForm.setFieldsValue({ remarks: ticket.remarks || "" });
    setResolveModalVisible(true);
  };

  // 关闭处理弹窗
  const handleCloseResolveModal = () => {
    setResolveModalVisible(false);
    setResolvingTicket(null);
    setResolveError(null);
    resolveForm.resetFields();
  };

  // 提交处理
  const handleConfirmResolve = async () => {
    if (!resolvingTicket) return;
    setResolveError(null);
    try {
      await resolveForm.validateFields();
      const values = resolveForm.getFieldsValue();
      const payload: ResolveTicketPayload = {
        remarks: values.remarks || undefined,
      };
      setIsResolving(true);
      await resolveProblemTicket(resolvingTicket.id, payload);
      message.success("问题订单处理成功！");
      handleCloseResolveModal();
      loadData(searchForm.getFieldsValue());
    } catch (error) {
      console.error("处理问题订单失败 - 完整错误对象:", error);
      try {
        console.log("错误对象序列化:", JSON.stringify(error, null, 2));
      } catch (e) {
        console.log("错误对象无法序列化:", e);
      }
      let errorMsg = "处理问题订单失败，请稍后重试";
      try {
        const apiError = error as ApiError;
        const statusCode = apiError.response?.status;
        const backendErrorMessage = apiError.response?.data?.errorMessage;
        console.log("状态码:", statusCode);
        console.log("后端错误信息:", backendErrorMessage);
        console.log("通用错误信息:", apiError.message);
        if (backendErrorMessage) {
          errorMsg = backendErrorMessage;
        } else if (statusCode && statusCode >= 500) {
          errorMsg = `服务器内部错误 (${statusCode})，请稍后重试`;
        } else if (apiError.message) {
          errorMsg = apiError.message;
        }
      } catch (parseError) {
        console.error("解析错误对象失败:", parseError);
      }
      setResolveError(errorMsg);
      message.error(errorMsg);
    } finally {
      setIsResolving(false);
    }
  };

  // 根据状态获取Badge状态
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
      case "PENDING_ACTION":
        return <Badge status="warning" text="待处理" />;
      case "IN_PROGRESS":
        return <Badge status="processing" text="处理中" />;
      case "RESOLVED":
        return <Badge status="success" text="已处理" />;
      case "CLOSED_UNRESOLVED":
        return <Badge status="default" text="关闭(未解决)" />;
      default:
        return <Badge status="default" text={status} />;
    }
  };

  // 获取优先级标签
  const getPriorityTag = (priority: number) => {
    const priorityInfo = PRIORITY_MAP[
      priority as keyof typeof PRIORITY_MAP
    ] || {
      color: "#d9d9d9",
      text: "未知",
    };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 打开修改备注弹窗
  const handleOpenRemarkModal = (ticket: ProblemTicket) => {
    setEditingTicket(ticket);
    remarkForm.setFieldsValue({ remarks: ticket.remarks || "" });
    setRemarkModalVisible(true);
    setRemarkError(null);
  };

  // 关闭修改备注弹窗
  const handleCloseRemarkModal = () => {
    setRemarkModalVisible(false);
    setEditingTicket(null);
    setRemarkError(null);
    remarkForm.resetFields();
  };

  // 提交备注修改
  const handleUpdateRemark = async () => {
    if (!editingTicket) return;
    try {
      await remarkForm.validateFields();
      const values = remarkForm.getFieldsValue();
      setIsUpdatingRemark(true);
      await updateProblemTicketRemark(editingTicket.id, values.remarks || "");
      message.success("备注修改成功！");
      handleCloseRemarkModal();
      loadData(searchForm.getFieldsValue());
    } catch (error) {
      console.error("修改备注失败:", error);
      let errorMsg = "修改备注失败，请稍后重试";
      try {
        const apiError = error as ApiError;
        const statusCode = apiError.response?.status;
        const backendErrorMessage = apiError.response?.data?.errorMessage;
        if (backendErrorMessage) {
          errorMsg = backendErrorMessage;
        } else if (statusCode && statusCode >= 500) {
          errorMsg = `服务器内部错误 (${statusCode})，请稍后重试`;
        } else if (apiError.message) {
          errorMsg = apiError.message;
        }
      } catch (parseError) {
        console.error("解析错误对象失败:", parseError);
      }
      setRemarkError(errorMsg);
      message.error(errorMsg);
    } finally {
      setIsUpdatingRemark(false);
    }
  };

  // 清除选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
  };

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  // 表格列定义
  const columns: TableColumnsType<ProblemTicket> = [
    {
      title: "序号",
      key: "index",
      width: 80,
      render: (_, __, index) => (current - 1) * pageSize + index + 1,
    },
    {
      title: "物流单号",
      dataIndex: "trackingNumber",
      key: "trackingNumber",
      width: 150,
    },
    {
      title: "客户",
      dataIndex: "customerNickname",
      key: "customerNickname",
      width: 100,
    },
    {
      title: "问题描述",
      dataIndex: "problemDescription",
      key: "problemDescription",
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status) => getStatusBadge(status),
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
      width: 80,
      render: (priority) => getPriorityTag(priority),
    },
    {
      title: "处理人",
      dataIndex: "assignedUserNickname",
      key: "assignedUserNickname",
      width: 100,
      render: (text) => text || "-",
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
    },
    {
      title: "处理备注",
      dataIndex: "remarks",
      key: "remarks",
      ellipsis: true,
      render: (text) =>
        text ? (
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        ) : (
          "-"
        ),
    },
    {
      title: "操作",
      key: "action",
      width: 220,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record.manifestId)}
          >
            详情
          </Button>

          {/* 待处理状态显示"修改备注"按钮 */}
          {record.status === "PENDING" && (
            <Button
              type="default"
              size="small"
              icon={<FormOutlined />}
              onClick={() => handleOpenRemarkModal(record)}
            >
              修改备注
            </Button>
          )}

          {/* 可处理状态显示"处理"按钮 */}
          {(record.status === "PENDING" || record.status === "IN_PROGRESS") && (
            <Button
              type="default"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleOpenResolveModal(record)}
            >
              处理
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 4.1 打开批量处理全部弹窗 (恢复)
  const handleOpenResolveAllModal = () => {
    resolveAllForm.resetFields();
    setResolveAllError(null);
    setResolveAllModalVisible(true);
  };

  // 4.2 关闭批量处理全部弹窗 (恢复)
  const handleCloseResolveAllModal = () => {
    setResolveAllModalVisible(false);
    resolveAllForm.resetFields();
    setResolveAllError(null);
  };

  // 4.3 确认批量处理全部 (恢复并调整)
  const handleConfirmResolveAll = async () => {
    try {
      await resolveAllForm.validateFields();
      const values = resolveAllForm.getFieldsValue();
      const payload: ResolveAllTicketsPayload = {
        remarks: values.remarks || undefined,
      };
      setIsResolvingAll(true);
      const result = await resolveAllProblemTickets(payload);
      setProcessedTicketsCount(result.processedCount);
      setCurrentBatchActionType("all");
      setBatchActionResultModalVisible(true); // 使用统一结果弹窗
      handleCloseResolveAllModal();
      loadData(searchForm.getFieldsValue());
    } catch (error) {
      const apiError = error as ApiError;
      const errorMsg =
        apiError.response?.data?.errorMessage ||
        (error as Error).message ||
        "批量处理全部失败";
      setResolveAllError(errorMsg);
      message.error(errorMsg);
    } finally {
      setIsResolvingAll(false);
    }
  };

  // 5.1 打开处理选中弹窗 (已存在，检查是否需要调整)
  const handleOpenResolveSpecificModal = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一个问题订单进行处理。");
      return;
    }
    resolveSpecificForm.resetFields();
    setResolveSpecificError(null);
    setResolveSpecificModalVisible(true);
  };

  // 5.2 关闭处理选中弹窗 (已存在)
  const handleCloseResolveSpecificModal = () => {
    setResolveSpecificModalVisible(false);
    resolveSpecificForm.resetFields();
    setResolveSpecificError(null);
  };

  // 5.3 确认处理选中 (已存在，调整为使用统一结果弹窗)
  const handleConfirmResolveSpecific = async () => {
    if (selectedRowKeys.length === 0) return;
    try {
      await resolveSpecificForm.validateFields();
      const values = resolveSpecificForm.getFieldsValue();
      const payload: ResolveSpecificTicketsPayload = {
        ticketIds: selectedRowKeys,
        remarks: values.remarks || undefined,
      };
      setIsResolvingSpecific(true);
      const result = await resolveSpecificProblemTickets(payload);
      setProcessedTicketsCount(result.processedCount);
      setCurrentBatchActionType("specific_resolved");
      setBatchActionResultModalVisible(true);
      handleCloseResolveSpecificModal();
      handleClearSelection();
      loadData(searchForm.getFieldsValue());
    } catch (error) {
      const apiError = error as ApiError;
      const errorMsg =
        apiError.response?.data?.errorMessage ||
        (error as Error).message ||
        "处理选中订单失败";
      setResolveSpecificError(errorMsg);
      message.error(errorMsg);
    } finally {
      setIsResolvingSpecific(false);
    }
  };

  // 7.1 打开标记选中为待处理弹窗
  const handleOpenMarkPendingModal = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一个问题订单。");
      return;
    }
    markPendingForm.resetFields();
    setMarkPendingError(null);
    setMarkPendingModalVisible(true);
  };

  // 7.2 关闭标记选中为待处理弹窗
  const handleCloseMarkPendingModal = () => {
    setMarkPendingModalVisible(false);
    markPendingForm.resetFields();
    setMarkPendingError(null);
  };

  // 7.3 确认标记选中为待处理
  const handleConfirmMarkPending = async () => {
    if (selectedRowKeys.length === 0) return;
    try {
      await markPendingForm.validateFields();
      const values = markPendingForm.getFieldsValue();
      const payload: MarkPendingTicketsPayload = {
        ticketIds: selectedRowKeys,
        remarks: values.remarks || undefined,
      };
      setIsMarkingPending(true);
      const result = await markSpecificTicketsAsPending(payload);
      setProcessedTicketsCount(result.processedCount);
      setCurrentBatchActionType("specific_pending");
      setBatchActionResultModalVisible(true);
      handleCloseMarkPendingModal();
      handleClearSelection();
      loadData(searchForm.getFieldsValue());
    } catch (error) {
      const apiError = error as ApiError;
      const errorMsg =
        apiError.response?.data?.errorMessage ||
        (error as Error).message ||
        "标记选中为待处理失败";
      setMarkPendingError(errorMsg);
      message.error(errorMsg);
    } finally {
      setIsMarkingPending(false);
    }
  };

  // 6.1 关闭统一的批量处理结果弹窗 (原handleCloseBatchActionResultModal)
  const handleCloseBatchActionResultModal = () => {
    setBatchActionResultModalVisible(false);
    setProcessedTicketsCount(null);
    setCurrentBatchActionType("");
  };

  // 新增：加载用户问题订单列表
  const loadUsersWithProblems = async (
    page: number = 1,
    pageSize: number = 10
  ) => {
    try {
      setUsersLoading(true);
      const result = await getUsersWithProblems({ page, pageSize });
      setUsersWithProblems(result.list || []);
      setUsersTotal(result.total || 0);
    } catch (error) {
      const apiError = error as ApiError;
      const errorMsg =
        apiError.response?.data?.errorMessage ||
        apiError.message ||
        "获取用户列表失败";
      message.error(errorMsg);
    } finally {
      setUsersLoading(false);
    }
  };

  // 新增：打开用户问题订单弹窗
  const handleOpenUsersModal = () => {
    setUsersModalVisible(true);
    loadUsersWithProblems(1, usersPageSize);
    setUsersCurrent(1);
    setSelectedUserId(null);
  };

  // 新增：关闭用户问题订单弹窗
  const handleCloseUsersModal = () => {
    setUsersModalVisible(false);
  };

  // 新增：用户列表分页变化处理
  const handleUsersTableChange = (pagination: TablePaginationConfig) => {
    const newCurrent = pagination.current || usersCurrent;
    const newPageSize = pagination.pageSize || usersPageSize;

    // 只在current或pageSize发生变化时更新状态并加载数据
    if (newCurrent !== usersCurrent || newPageSize !== usersPageSize) {
      setUsersCurrent(newCurrent);
      setUsersPageSize(newPageSize);
      loadUsersWithProblems(newCurrent, newPageSize);
    }
  };

  // 新增：选择用户并查询其问题订单
  const handleSelectUser = (userId: number, nickname: string) => {
    setSelectedUserId(userId);
    setSelectedUserNickname(nickname);
    handleCloseUsersModal();
    searchForm.setFieldsValue({
      customerId: userId,
      status: "PENDING",
      problemTypeCode: "",
    });
    setCurrent(1);
    loadData({
      page: 1,
      pageSize,
      customerId: userId,
      status: "PENDING",
    });
  };

  // 新增：用户列表表格列定义
  const userColumns: TableColumnsType<UserWithProblems> = [
    {
      title: "用户ID",
      dataIndex: "userId",
      key: "userId",
      width: 100,
    },
    {
      title: "用户名称",
      dataIndex: "nickname",
      key: "nickname",
    },
    {
      title: "待处理订单数量",
      dataIndex: "pendingCount",
      key: "pendingCount",
      width: 150,
      render: (count) => <Tag color="orange">{count}</Tag>,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_, record) => (
        <Button
          type="primary"
          size="small"
          onClick={() => handleSelectUser(record.userId, record.nickname)}
        >
          查看问题订单
        </Button>
      ),
    },
  ];

  return (
    <>
      <Card title="问题订单管理" bordered={false}>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="horizontal"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
          initialValues={{ status: "PENDING", problemTypeCode: "" }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="物流单号" name="trackingNumber">
                <Input placeholder="请输入物流单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="状态" name="status">
                <Select
                  placeholder="选择工单状态"
                  options={SIMPLIFIED_STATUS_OPTIONS}
                  onChange={() => searchForm.submit()}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="问题类型" name="problemTypeCode">
                <Select
                  placeholder="选择问题类型"
                  options={PROBLEM_TYPE_FILTER_OPTIONS}
                  onChange={() => searchForm.submit()}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={24} lg={10}>
              <Form.Item label="创建时间" name="dateRange">
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            {/* 隐藏字段 - 用户ID */}
            <Form.Item name="customerId" hidden>
              <Input />
            </Form.Item>
          </Row>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={16} lg={16} style={{ textAlign: "left" }}>
              <Space size="middle" wrap>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={handleOpenResolveAllModal}
                >
                  全部标记为已处理
                </Button>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={handleOpenResolveSpecificModal}
                  disabled={selectedRowKeys.length === 0}
                >
                  标记选中为已处理
                </Button>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleOpenMarkPendingModal}
                  disabled={selectedRowKeys.length === 0}
                >
                  标记选中为待处理
                </Button>
                <Button
                  type="default"
                  icon={<UserOutlined />}
                  onClick={handleOpenUsersModal}
                  style={{ zIndex: 1 }} /* 增加按钮的层级 */
                >
                  按用户查看
                </Button>
              </Space>
            </Col>
            <Col xs={24} sm={24} md={8} lg={8} style={{ textAlign: "right" }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                >
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>

        {/* 显示选中的用户 */}
        {selectedUserId && (
          <Alert
            message={`正在查看用户: ${selectedUserNickname} 的问题订单`}
            type="info"
            showIcon
            closable
            onClose={() => {
              setSelectedUserId(null);
              setSelectedUserNickname("");
              handleReset();
            }}
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 新增：批量操作区域 */}
        {selectedRowKeys.length > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Alert
              message={
                <span>
                  已选择 <strong>{selectedRowKeys.length}</strong> 项
                  <Button
                    type="link"
                    size="small"
                    onClick={handleClearSelection}
                  >
                    清除
                  </Button>
                </span>
              }
              type="info"
              showIcon
            />
          </div>
        )}

        {/* 数据表格 */}
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (totalCount) => `共 ${totalCount} 条记录`,
            responsive: true,
          }}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: "max-content" }}
          bordered
          size="middle"
        />
      </Card>

      {/* 添加详情抽屉组件 */}
      <ProblemTicketDetailDrawer
        manifestId={selectedManifestId}
        visible={detailDrawerVisible}
        onClose={closeDetailDrawer}
      />

      {/* 处理问题订单弹窗 */}
      {resolvingTicket && (
        <Modal
          title={`处理问题订单 - ${resolvingTicket.trackingNumber}`}
          open={resolveModalVisible}
          onCancel={handleCloseResolveModal}
          footer={[
            <Button
              key="back"
              onClick={handleCloseResolveModal}
              disabled={isResolving}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isResolving}
              onClick={handleConfirmResolve}
            >
              处理完成
            </Button>,
          ]}
        >
          {/* 在Modal内部显示错误信息 */}
          {resolveError && (
            <Alert
              message="处理失败"
              description={resolveError}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Form form={resolveForm} layout="vertical">
            <Form.Item name="remarks" label="处理备注 (可选)">
              <TextArea rows={4} placeholder="请输入处理备注信息" />
            </Form.Item>
          </Form>
        </Modal>
      )}

      {/* 修改备注弹窗 */}
      {editingTicket && (
        <Modal
          title={`修改备注 - ${editingTicket.trackingNumber}`}
          open={remarkModalVisible}
          onCancel={handleCloseRemarkModal}
          footer={[
            <Button
              key="back"
              onClick={handleCloseRemarkModal}
              disabled={isUpdatingRemark}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={isUpdatingRemark}
              onClick={handleUpdateRemark}
            >
              保存
            </Button>,
          ]}
        >
          {/* 在Modal内部显示错误信息 */}
          {remarkError && (
            <Alert
              message="操作失败"
              description={remarkError}
              type="error"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Form form={remarkForm} layout="vertical">
            <Form.Item
              name="remarks"
              label="备注信息"
              rules={[{ max: 500, message: "备注最多500个字符" }]}
            >
              <TextArea
                rows={4}
                placeholder="请输入备注信息"
                showCount
                maxLength={500}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}

      {/* 批量处理全部弹窗 (恢复) */}
      <Modal
        title="批量处理所有待处理问题订单"
        open={resolveAllModalVisible}
        onCancel={handleCloseResolveAllModal}
        footer={[
          <Button
            key="back"
            onClick={handleCloseResolveAllModal}
            disabled={isResolvingAll}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isResolvingAll}
            onClick={handleConfirmResolveAll}
          >
            确认批量处理
          </Button>,
        ]}
      >
        {resolveAllError && (
          <Alert
            message="处理失败"
            description={resolveAllError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Alert
          message="操作确认"
          description="此操作将把所有待处理的问题订单标记为已处理。请确认操作。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Form form={resolveAllForm} layout="vertical">
          <Form.Item
            name="remarks"
            label="处理备注 (可选)"
            rules={[{ max: 500, message: "备注最多500个字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入批量处理的原因或备注信息"
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 处理选中问题订单弹窗 (已存在) */}
      <Modal
        title="处理选中的问题订单"
        open={resolveSpecificModalVisible}
        onCancel={handleCloseResolveSpecificModal}
        footer={[
          <Button
            key="back"
            onClick={handleCloseResolveSpecificModal}
            disabled={isResolvingSpecific}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isResolvingSpecific}
            onClick={handleConfirmResolveSpecific}
          >
            确认处理选中
          </Button>,
        ]}
      >
        {resolveSpecificError && (
          <Alert
            message="处理失败"
            description={resolveSpecificError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Alert
          message={`您已选择 ${selectedRowKeys.length} 个问题订单进行处理。`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Form form={resolveSpecificForm} layout="vertical">
          <Form.Item
            name="remarks"
            label="处理备注 (可选)"
            rules={[{ max: 500, message: "备注最多500个字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入处理备注信息"
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增：标记选中为待处理弹窗 */}
      <Modal
        title="标记选中的问题订单为待处理"
        open={markPendingModalVisible}
        onCancel={handleCloseMarkPendingModal}
        footer={[
          <Button
            key="back"
            onClick={handleCloseMarkPendingModal}
            disabled={isMarkingPending}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isMarkingPending}
            onClick={handleConfirmMarkPending}
          >
            确认标记
          </Button>,
        ]}
      >
        {markPendingError && (
          <Alert
            message="操作失败"
            description={markPendingError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Alert
          message={`您已选择 ${selectedRowKeys.length} 个问题订单。`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        <Form form={markPendingForm} layout="vertical">
          <Form.Item
            name="remarks"
            label="处理备注 (可选)"
            rules={[{ max: 500, message: "备注最多500个字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入备注信息"
              showCount
              maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 统一的批量处理结果弹窗 (修改title) */}
      <Modal
        title={
          currentBatchActionType === "all"
            ? "批量处理全部 - 结果"
            : currentBatchActionType === "specific_resolved"
            ? "处理选中(已处理) - 结果"
            : currentBatchActionType === "specific_pending"
            ? "处理选中(待处理) - 结果"
            : "操作结果"
        }
        open={batchActionResultModalVisible}
        onCancel={handleCloseBatchActionResultModal}
        footer={[
          <Button
            key="ok"
            type="primary"
            onClick={handleCloseBatchActionResultModal}
          >
            知道了
          </Button>,
        ]}
      >
        {processedTicketsCount !== null && (
          <p>操作成功，共处理了 {processedTicketsCount} 个问题订单！</p>
        )}
      </Modal>

      {/* 新增：用户问题订单弹窗 */}
      <Modal
        title="用户问题订单统计"
        open={usersModalVisible}
        onCancel={handleCloseUsersModal}
        footer={[
          <Button key="back" onClick={handleCloseUsersModal}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        <Table
          columns={userColumns}
          dataSource={usersWithProblems}
          rowKey="userId"
          loading={usersLoading}
          pagination={{
            current: usersCurrent,
            pageSize: usersPageSize,
            total: usersTotal,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            responsive: true,
          }}
          onChange={handleUsersTableChange}
          bordered
          size="middle"
        />
      </Modal>
    </>
  );
};

export default ProblemTicketPage;
