import React, { useState, useEffect } from "react";
import {
  Typography,
  Steps,
  Card,
  Upload,
  Button,
  Progress,
  message,
  Space,
  Table,
  Tag,
  Tooltip,
  Input,
  Switch,
  Row,
  Col,
  Divider,
  Statistic,
  Descriptions,
} from "antd";
import type {
  UploadFile,
  UploadChangeParam,
  RcFile,
} from "antd/es/upload/interface";
import type { ColumnsType } from "antd/es/table";
import {
  InboxOutlined,
  ArrowLeftOutlined,
  QuestionCircleOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  EditOutlined,
  ToolOutlined,
  CheckCircleFilled,
} from "@ant-design/icons";
import styles from "./OrderForecast.module.css";

const { Title, Text, Link } = Typography;
const { Step } = Steps;
const { Dragger } = Upload;

// --- Mock Data for Preview Table ---
interface ForecastRecord {
  key: string;
  rowIndex: number;
  trackingNumber?: string;
  recipientName: string;
  recipientPhone: string;
  recipientAddress: string;
  itemDescription: string;
  quantity: number;
  weight: number;
  declaredValue: number;
  validationStatus: "valid" | "warning" | "error";
  errors?: { [field: string]: string }; // Store errors per field
}

const mockPreviewData: ForecastRecord[] = [
  {
    key: "1",
    rowIndex: 1,
    trackingNumber: "PREVIEW001",
    recipientName: "张三",
    recipientPhone: "13800138000",
    recipientAddress: "上海市浦东新区XX路1号",
    itemDescription: "服装",
    quantity: 2,
    weight: 1.5,
    declaredValue: 50,
    validationStatus: "valid",
  },
  {
    key: "2",
    rowIndex: 2,
    trackingNumber: "PREVIEW002",
    recipientName: "李四",
    recipientPhone: "1391234567",
    recipientAddress: "北京市朝阳区YY街2号",
    itemDescription: "电子产品",
    quantity: 1,
    weight: 0.8,
    declaredValue: 200,
    validationStatus: "warning",
    errors: { recipientPhone: "电话号码格式疑似不正确" },
  },
  {
    key: "3",
    rowIndex: 3,
    trackingNumber: "PREVIEW003",
    recipientName: "王五",
    recipientPhone: "137abcdefg",
    recipientAddress: "广东省深圳市ZZ大道3号",
    itemDescription: "书籍",
    quantity: 5,
    weight: 2.1,
    declaredValue: 30,
    validationStatus: "error",
    errors: {
      recipientPhone: "电话号码包含无效字符",
      declaredValue: "申报价值过低",
    },
  },
  {
    key: "4",
    rowIndex: 4,
    trackingNumber: "PREVIEW004",
    recipientName: "赵六",
    recipientPhone: "13600136000",
    recipientAddress: "浙江省杭州市西湖区AA路4号",
    itemDescription: "化妆品（可能需要特殊处理）",
    quantity: 3,
    weight: 0.5,
    declaredValue: 80,
    validationStatus: "warning",
    errors: { itemDescription: "物品描述可能触发敏感词" },
  },
  {
    key: "5",
    rowIndex: 5,
    trackingNumber: "PREVIEW005",
    recipientName: "钱七",
    recipientPhone: "13501234567",
    recipientAddress: "",
    itemDescription: "玩具",
    quantity: 1,
    weight: 1.2,
    declaredValue: 25,
    validationStatus: "error",
    errors: { recipientAddress: "收件人地址不能为空" },
  },
  // Add more mock data as needed...
];
// ---------------------------------

const OrderForecast: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  // State for table selection
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // State for records in the confirmation step
  const [confirmedRecords, setConfirmedRecords] = useState<ForecastRecord[]>(
    []
  );

  // Effect to initialize confirmed records when moving to step 2
  useEffect(() => {
    if (currentStep === 2) {
      const recordsToSubmit = mockPreviewData.filter(
        (r) =>
          r.validationStatus === "valid" || r.validationStatus === "warning"
      );
      setConfirmedRecords(recordsToSubmit);
    }
  }, [currentStep]); // Run effect when currentStep changes

  const handleUploadChange = (info: UploadChangeParam<UploadFile>) => {
    let currentFileList = [...info.fileList];
    currentFileList = currentFileList.slice(-1); // Keep only the last file

    if (info.file.status === "uploading") {
      if (
        !uploading &&
        currentFileList.length > 0 &&
        currentFileList[0].originFileObj
      ) {
        setUploading(true);
        setProgress(0);
        const interval = setInterval(() => {
          setProgress((prevProgress) => {
            const newProgress = prevProgress + 10;
            if (newProgress >= 100) {
              clearInterval(interval);
              setUploading(false);
              if (info.file.name) {
                message.success(`${info.file.name} 文件上传成功，等待验证.`);
              }
              setCurrentStep(1);
              return 100;
            }
            return newProgress;
          });
        }, 200);
      }
    } else if (info.file.status === "done") {
      // This part might be handled by the mock progress completion
      // message.success(`${info.file.name} 文件上传成功.`);
      // setUploading(false);
      // setCurrentStep(1); // Move to preview step
    } else if (info.file.status === "error") {
      if (info.file.name) {
        message.error(`${info.file.name} 文件上传失败.`);
      }
      setUploading(false);
      setProgress(0);
    }

    setFileList(currentFileList);
  };

  const draggerProps = {
    name: "file",
    multiple: false,
    beforeUpload: (file: RcFile): boolean => {
      const isExcel =
        file.type === "application/vnd.ms-excel" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!isExcel) {
        message.error("只能上传 Excel 文件 (.xls, .xlsx)!");
      }
      return isExcel;
    },
    onChange: handleUploadChange,
    fileList,
    showUploadList: false,
  };

  // --- Table Columns Definition ---
  const columns: ColumnsType<ForecastRecord> = [
    {
      title: "#", // 行号
      dataIndex: "rowIndex",
      key: "rowIndex",
      width: 60,
      fixed: "left", // 固定在左侧
    },
    {
      title: "状态",
      dataIndex: "validationStatus",
      key: "validationStatus",
      width: 80,
      fixed: "left",
      render: (
        status: ForecastRecord["validationStatus"],
        record: ForecastRecord
      ) => {
        let icon = null;
        let color = "";
        let text = "";
        switch (status) {
          case "valid":
            icon = <CheckCircleOutlined style={{ color: "green" }} />;
            color = "success";
            text = "有效";
            break;
          case "warning":
            icon = <WarningOutlined style={{ color: "orange" }} />;
            color = "warning";
            text = "警告";
            break;
          case "error":
            icon = <CloseCircleOutlined style={{ color: "red" }} />;
            color = "error";
            text = "错误";
            break;
        }
        // 如果有错误信息，则使用 Tooltip 包裹
        const errorMessages = record.errors
          ? Object.entries(record.errors)
              .map(([field, msg]) => `${field}: ${msg}`)
              .join("\n")
          : "";
        return errorMessages ? (
          <Tooltip
            title={
              <div style={{ whiteSpace: "pre-line" }}>{errorMessages}</div>
            }
          >
            <Tag icon={icon} color={color}>
              {text}
            </Tag>
          </Tooltip>
        ) : (
          <Tag icon={icon} color={color}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: "预报单号",
      dataIndex: "trackingNumber",
      key: "trackingNumber",
      width: 150,
    },
    {
      title: "收件人姓名",
      dataIndex: "recipientName",
      key: "recipientName",
      width: 120,
      // TODO: Add editable cell logic
    },
    {
      title: "收件人电话",
      dataIndex: "recipientPhone",
      key: "recipientPhone",
      width: 150,
      render: (text, record) =>
        record.errors?.recipientPhone ? (
          <span className={styles.errorCell}>{text}</span>
        ) : (
          text
        ),
    },
    {
      title: "收件人地址",
      dataIndex: "recipientAddress",
      key: "recipientAddress",
      width: 300,
      render: (text, record) =>
        record.errors?.recipientAddress ? (
          <span className={styles.errorCell}>{text || "[空]"}</span>
        ) : (
          text
        ),
    },
    {
      title: "物品描述",
      dataIndex: "itemDescription",
      key: "itemDescription",
      width: 200,
      render: (text, record) =>
        record.errors?.itemDescription ? (
          <Tooltip title={record.errors.itemDescription}>
            <span className={styles.warningCell}>{text}</span>
          </Tooltip>
        ) : (
          text
        ),
    },
    { title: "数量", dataIndex: "quantity", key: "quantity", width: 80 },
    { title: "重量(KG)", dataIndex: "weight", key: "weight", width: 100 },
    {
      title: "申报价值($)",
      dataIndex: "declaredValue",
      key: "declaredValue",
      width: 120,
    },
    // Add more columns as needed
  ];
  // -------------------------------

  // --- Table Row Selection Logic ---
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    console.log("selectedRowKeys changed: ", newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  // -------------------------------

  // --- Handler to remove a record from the confirmation list ---
  const handleRemoveRecord = (key: React.Key) => {
    setConfirmedRecords((prevRecords) =>
      prevRecords.filter((record) => record.key !== key)
    );
    // Optionally show a message
    message.info(`记录 (Key: ${key}) 已从提交列表中移除。`);
  };
  // -------------------------------------------------------------

  // --- UI Rendering based on currentStep ---
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: {
        return (
          <Card className={styles.uploadCard}>
            <Dragger {...draggerProps} className={styles.dragger}>
              {uploading ? (
                <div className={styles.progressContainer}>
                  <Progress type="circle" percent={progress} />
                  <Text style={{ marginTop: 15 }}>
                    正在上传: {fileList[0]?.name}
                  </Text>
                  {/* Add Cancel Button if needed */}
                </div>
              ) : (
                <>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">
                    拖放Excel文件至此或点击选择文件
                  </p>
                  <p className="ant-upload-hint">
                    支持 .xls, .xlsx 格式。为确保顺利导入，请使用提供的模板。
                  </p>
                </>
              )}
            </Dragger>
            <div className={styles.uploadActions}>
              <Button icon={<DownloadOutlined />}>下载模板</Button>{" "}
              {/* TODO: Add Dropdown for multiple templates */}
              <Link href="#" target="_blank" style={{ marginLeft: 15 }}>
                查看填写说明
              </Link>
            </div>
            <div className={styles.lastUploadInfo}>
              <Text type="secondary">
                上一次成功上传: 2024-07-25 10:30 - 文件名.xlsx (150条)
              </Text>{" "}
              {/* Placeholder */}
            </div>
          </Card>
        );
      }
      case 1: {
        const validCount = mockPreviewData.filter(
          (r) => r.validationStatus === "valid"
        ).length;
        const warningCount = mockPreviewData.filter(
          (r) => r.validationStatus === "warning"
        ).length;
        const errorCount = mockPreviewData.filter(
          (r) => r.validationStatus === "error"
        ).length;
        const totalCount = mockPreviewData.length;

        return (
          <div className={styles.previewContainer}>
            {/* 1. Validation Summary Card */}
            <Card bordered={false} className={styles.summaryCard}>
              <Row gutter={16} align="middle">
                <Col flex="auto">
                  <Space direction="vertical">
                    <Title level={4} style={{ margin: 0 }}>
                      数据验证结果
                    </Title>
                    <Space split={<Divider type="vertical" />}>
                      <Statistic title="总记录" value={totalCount} />
                      <Statistic
                        title="有效"
                        value={validCount}
                        valueStyle={{ color: "#52c41a" }}
                      />
                      <Statistic
                        title="警告"
                        value={warningCount}
                        valueStyle={{ color: "#faad14" }}
                      />
                      <Statistic
                        title="错误"
                        value={errorCount}
                        valueStyle={{ color: "#ff4d4f" }}
                      />
                    </Space>
                  </Space>
                </Col>
                <Col>
                  {/* TODO: Add ratio chart and error distribution chart */}
                  <Text type="secondary">图表区域占位</Text>
                </Col>
                <Col>
                  <Space direction="vertical" align="end">
                    <Button icon={<ToolOutlined />}>
                      一键修复可自动修正项
                    </Button>
                    <Space>
                      <Text>仅保留有效记录:</Text> <Switch size="small" />
                      <Link href="#">导出验证结果</Link>
                    </Space>
                  </Space>
                </Col>
              </Row>
            </Card>

            {/* 2. Data Table Area */}
            <Card
              bordered={false}
              style={{ marginTop: 16 }}
              bodyStyle={{ padding: 0 }}
            >
              {/* Table Toolbar */}
              <div className={styles.tableToolbar}>
                <Space>
                  <Button icon={<FilterOutlined />}>列显示</Button>{" "}
                  {/* TODO: Column visibility control */}
                  <Input
                    addonBefore="搜索"
                    prefix={<SearchOutlined />}
                    placeholder="输入关键字..."
                    style={{ width: 250 }}
                  />
                </Space>
                <Space>
                  <Button icon={<EditOutlined />}>批量编辑</Button>
                  {/* TODO: View Switch */}
                </Space>
              </div>
              {/* Table */}
              <Table<ForecastRecord>
                columns={columns}
                dataSource={mockPreviewData}
                rowSelection={rowSelection}
                pagination={{ pageSize: 50 }} // Adjust pagination as needed
                scroll={{ x: 1500, y: 400 }} // Enable horizontal and vertical scroll
                rowClassName={(record) =>
                  styles[`row-${record.validationStatus}`] || ""
                } // Apply row styles
                sticky // Enable sticky header and scroll bar
              />
            </Card>

            {/* 3. Bottom Action Bar */}
            <div className={styles.bottomActionBar}>
              <Space>
                <Text>
                  已选择 {selectedRowKeys.length} / {totalCount} 条记录
                </Text>
              </Space>
              <Space>
                <Text type="secondary">
                  当前状态: 验证完成，发现 {errorCount} 处错误， {warningCount}{" "}
                  处警告
                </Text>
              </Space>
              <Space>
                <Button onClick={() => setCurrentStep(0)}>放弃</Button>
                <Button>重新验证</Button> {/* TODO: Add re-validation logic */}
                <Button type="primary" onClick={() => setCurrentStep(2)}>
                  提交预报 ({validCount + warningCount})
                </Button>
              </Space>
            </div>
          </div>
        );
      }
      case 2: {
        // Confirm Step - Now uses confirmedRecords state

        // Calculate summary based on the current confirmedRecords state
        const totalValidCount = confirmedRecords.length; // Use state length
        const totalWeight = confirmedRecords
          .reduce((sum, r) => sum + r.weight, 0)
          .toFixed(2);
        const totalPackages = confirmedRecords.reduce(
          (sum, r) => sum + r.quantity,
          0
        );
        const estimatedCost = confirmedRecords
          .reduce((sum, r) => sum + r.declaredValue * 0.1 + r.weight * 5, 0) // Example calculation
          .toFixed(2);

        // Define columns for the simplified confirmation list
        const confirmColumns: ColumnsType<ForecastRecord> = [
          {
            title: "预报单号",
            dataIndex: "trackingNumber",
            key: "trackingNumber",
            width: 150,
          },
          {
            title: "收件人",
            dataIndex: "recipientName",
            key: "recipientName",
            width: 120,
          },
          {
            title: "收件人地址",
            dataIndex: "recipientAddress",
            key: "recipientAddress",
            ellipsis: true,
          }, // Use ellipsis for long text
          { title: "重量(KG)", dataIndex: "weight", key: "weight", width: 100 },
          {
            title: "价值($)",
            dataIndex: "declaredValue",
            key: "declaredValue",
            width: 100,
          },
          {
            title: "操作",
            key: "action",
            width: 80,
            render: (_, record) => (
              // Call handleRemoveRecord onClick
              <Link onClick={() => handleRemoveRecord(record.key)}>移除</Link>
            ),
          },
        ];

        return (
          <div className={styles.confirmContainer}>
            {/* 1. Confirmation Summary Card */}
            <Card
              bordered={false}
              className={styles.summaryCard} // Keep existing class for styling
              title="预报信息确认"
            >
              {/* Descriptions now use dynamically calculated values */}
              <Descriptions bordered column={2} size="small">
                <Descriptions.Item label="提交记录数">
                  {totalValidCount} 条
                </Descriptions.Item>
                <Descriptions.Item label="总包裹数">
                  {totalPackages} 件
                </Descriptions.Item>
                <Descriptions.Item label="总重量">
                  {totalWeight} KG
                </Descriptions.Item>
                <Descriptions.Item label="预估费用">
                  ${estimatedCost}
                </Descriptions.Item>
                {/* TODO: Add Customer/Destination distribution charts here */}
                <Descriptions.Item label="客户分布" span={1}>
                  图表占位
                </Descriptions.Item>
                <Descriptions.Item label="目的地分布" span={1}>
                  图表占位
                </Descriptions.Item>
                <Descriptions.Item label="费用明细" span={2}>
                  运输费: $XXX, 燃油附加费: $YYY, 其他: $ZZZ (占位)
                </Descriptions.Item>
                <Descriptions.Item label="特殊费用提醒" span={2}>
                  <Text type="warning">
                    包含XX件化妆品，可能产生额外处理费。
                  </Text>{" "}
                  (占位)
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 2. Records List to Submit */}
            <Card
              bordered={false}
              title="待提交记录预览"
              style={{ marginTop: 16 }}
              bodyStyle={{ padding: 0 }}
              // Add a class for specific styling if needed
              // className={styles.confirmTableCard}
            >
              <Table<ForecastRecord>
                columns={confirmColumns}
                dataSource={confirmedRecords} // Use state for dataSource
                pagination={{ pageSize: 5, simple: true }} // Simple pagination for preview
                scroll={{ y: 300 }} // Limit height
                size="small"
                rowKey="key" // Ensure rowKey is specified for state updates
              />
            </Card>

            {/* 3. Confirmation Actions */}
            <Card
              bordered={false}
              style={{ marginTop: 16 }}
              // Add a class for specific styling if needed
              // className={styles.confirmActionsCard}
            >
              <Row gutter={16} align="middle">
                <Col flex="auto">
                  <Input.TextArea
                    rows={2}
                    placeholder="可选：为该批次预报添加备注信息..."
                  />
                </Col>
                <Col>
                  <Button
                    type="primary"
                    size="large"
                    icon={<CheckCircleFilled />}
                    onClick={() =>
                      message.success(
                        `批次 ${Date.now()} 提交成功！共 ${totalValidCount} 条记录。` // Use updated count
                      )
                    } // Mock submission
                    disabled={totalValidCount === 0} // Disable if no records left
                  >
                    确认提交预报
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* Navigation Buttons */}
            <div className={styles.confirmNavigation}>
              {" "}
              {/* Add class for styling */}
              <Button onClick={() => setCurrentStep(1)}>返回预览编辑</Button>
            </div>
          </div>
        );
      }
      default:
        return <div>未知步骤</div>;
    }
  };

  return (
    <div className={styles.forecastContainer}>
      {/* Header */}
      <div className={styles.pageHeader}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            预报管理
          </Title>
          <Text type="secondary">Excel批量预报</Text>
        </div>
        <Space>
          <Button icon={<ArrowLeftOutlined />}>返回</Button>{" "}
          {/* TODO: Implement back navigation */}
          <Button
            type="text"
            shape="circle"
            icon={<QuestionCircleOutlined />}
          />
        </Space>
      </div>

      {/* Steps Indicator */}
      <Steps current={currentStep} className={styles.stepsIndicator}>
        <Step title="上传文件" description="选择或拖放Excel文件" />
        <Step title="验证编辑" description="检查数据格式与内容" />
        <Step title="确认提交" description="核对信息并最终提交" />
      </Steps>

      {/* Step Content */}
      <div className={styles.stepContent}>{renderStepContent()}</div>
    </div>
  );
};

export default OrderForecast;
