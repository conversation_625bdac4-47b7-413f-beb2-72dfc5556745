import { apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 业务类型枚举
export enum BusinessType {
  COMPENSATION_PROOF = "compensation_proof", // 赔偿证明
  ADJUSTMENT_PROOF = "adjustment_proof", // 调整证明
}

// 图片上传响应数据接口
export interface ImageUploadData {
  fileUrl: string; // 文件完整URL
  fileName: string; // 文件名（相对路径）
  fileSize: number; // 文件大小（字节）
  uploadTime: string; // 上传时间
}

// 图片上传响应接口
export interface ImageUploadResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: ImageUploadData;
}

// 图片删除响应数据接口
export interface ImageDeleteData {
  message: string;
}

// 图片删除响应接口
export interface ImageDeleteResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: ImageDeleteData;
}

// 上传配置信息接口
export interface UploadConfig {
  maxFileSize: string; // 最大文件大小
  supportedTypes: string[]; // 支持的文件类型
  businessTypes: string[]; // 业务类型
  uploadEndpoint: string; // 上传接口端点
  deleteEndpoint: string; // 删除接口端点
}

// 上传配置响应接口
export interface UploadConfigResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: UploadConfig;
}

/**
 * 上传图片文件
 * @param file 要上传的图片文件
 * @param businessType 业务类型
 * @returns 上传结果，包含图片URL等信息
 */
export const uploadImage = async (
  file: File,
  businessType: BusinessType
): Promise<ImageUploadData> => {
  // 创建FormData对象
  const formData = new FormData();
  formData.append("file", file);
  formData.append("businessType", businessType);

  // 发送上传请求
  const response = await apiClient.post<ImageUploadResponse>(
    getApiPath("/upload/image"),
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );

  return response.data.data;
};

/**
 * 删除图片文件
 * @param fileUrl 要删除的图片文件URL
 * @returns 删除结果
 */
export const deleteImage = async (
  fileUrl: string
): Promise<ImageDeleteData> => {
  const response = await apiClient.delete<ImageDeleteResponse>(
    getApiPath("/upload/image"),
    {
      params: {
        fileUrl,
      },
    }
  );

  return response.data.data;
};

/**
 * 获取上传配置信息
 * @returns 上传配置信息
 */
export const getUploadConfig = async (): Promise<UploadConfig> => {
  const response = await apiClient.get<UploadConfigResponse>(
    getApiPath("/upload/info")
  );

  return response.data.data;
};

// 文件验证工具函数
export const validateImageFile = (file: File): string | null => {
  // 检查文件大小（10MB = 10 * 1024 * 1024 字节）
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
    return `文件大小超出限制，最大支持10MB，当前文件大小：${sizeMB}MB`;
  }

  // 检查文件类型
  const supportedTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
  const fileExtension = file.name.split(".").pop()?.toLowerCase();

  if (!fileExtension || !supportedTypes.includes(fileExtension)) {
    return `不支持的图片格式，支持的格式：${supportedTypes.join(", ")}`;
  }

  return null; // 验证通过
};

// 业务类型选项，用于前端选择
export const BUSINESS_TYPE_OPTIONS = [
  { value: BusinessType.COMPENSATION_PROOF, label: "赔偿证明" },
  { value: BusinessType.ADJUSTMENT_PROOF, label: "调整证明" },
];

/**
 * 格式化业务类型为中文显示
 * @param businessType 业务类型
 * @returns 中文描述
 */
export const formatBusinessType = (businessType: BusinessType): string => {
  const typeMap = {
    [BusinessType.COMPENSATION_PROOF]: "赔偿证明",
    [BusinessType.ADJUSTMENT_PROOF]: "调整证明",
  };
  return typeMap[businessType] || businessType;
};

/**
 * 从文件URL提取文件名
 * @param fileUrl 文件完整URL
 * @returns 文件名
 */
export const extractFileNameFromUrl = (fileUrl: string): string => {
  try {
    const url = new URL(fileUrl);
    const pathname = url.pathname;
    return pathname.split("/").pop() || "unknown";
  } catch {
    return "unknown";
  }
};

/**
 * 格式化文件大小为可读格式
 * @param sizeInBytes 文件大小（字节）
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (sizeInBytes: number): string => {
  const units = ["B", "KB", "MB", "GB"];
  let size = sizeInBytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
};
