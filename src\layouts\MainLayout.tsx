import React, { useState, useEffect } from "react"; // 确保 React 和 useState 被导入，添加 useEffect
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Space,
  Breadcrumb,
  Badge,
  Tooltip,
  MenuProps,
} from "antd";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ContainerOutlined,
  NodeIndexOutlined,
  GoldOutlined,
  UserOutlined,
  PayCircleOutlined,
  AreaChartOutlined,
  SettingOutlined,
  BellOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  LogoutOutlined,
  EditOutlined,
  CalculatorOutlined,
} from "@ant-design/icons";
import styles from "./MainLayout.module.css";
// 移除 Dashboard 的直接导入，它将通过 Outlet 渲染
// import Dashboard from "../pages/Dashboard/Dashboard";
import { Outlet, useNavigate, useLocation } from "react-router-dom"; // 引入 Outlet, useNavigate, useLocation
import MenuBadge from "../components/MenuBadge";

const { Header, Sider, Content } = Layout;

// 定义菜单项类型别名 (Ant Design Menu ItemType)
type MenuItem = Required<MenuProps>["items"][number];

// 辅助函数创建菜单项，确保类型正确
function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: "group"
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type,
  } as MenuItem;
}

// 使用 getItem 函数创建菜单项
const menuItems: MenuItem[] = [
  getItem("仪表盘/工作台", "/dashboard", <DashboardOutlined />),
  getItem("订单管理", "/orders", <ContainerOutlined />, [
    getItem("订单列表", "/orders/list"),
    getItem("订单预报", "/orders/forecast"),
    getItem("问题订单", "/orders/problem-tickets"),
  ]),
  getItem(<MenuBadge title="预报管理" />, "/forecast", <GoldOutlined />, [
    getItem(<MenuBadge title="预报审核" />, "/forecast/audit"),
  ]),
  getItem("运单追踪", "/tracking", <NodeIndexOutlined />),
  getItem("仓储管理", "/warehouse", <GoldOutlined />),
  getItem("运费模板管理", "/shipping-fee", <CalculatorOutlined />, [
    getItem("模板管理", "/shipping-fee/templates"),
    getItem("用户模板配置", "/shipping-fee/user-config"),
  ]),
  getItem("客户管理", "/customers", <UserOutlined />),
  getItem("财务管理", "/billing-finance", <PayCircleOutlined />, [
    // getItem("账单批次管理", "/billing-finance/cycles"),
    getItem("账单管理", "/billing-finance/cycles"),
    getItem("费用调整", "/billing-finance/adjustments"),
  ]),
  getItem("报表分析", "/reports", <AreaChartOutlined />),
  getItem("系统设置", "/settings", <SettingOutlined />, [
    getItem("用户管理", "/settings/users"),
    getItem("权限管理", "/settings/roles"),
  ]),
];

// 用户下拉菜单项直接使用 MenuProps['items'] 定义
const userMenuItems: MenuProps["items"] = [
  { key: "profile", icon: <UserOutlined />, label: "个人中心" },
  { key: "changePassword", icon: <EditOutlined />, label: "修改密码" },
  { type: "divider" }, // 分隔线
  { key: "logout", icon: <LogoutOutlined />, label: "退出登录", danger: true },
];

// 添加用户信息接口
interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  avatar?: string;
}

// 定义 MainLayout 的 Props，包含 onLogout
interface MainLayoutProps {
  onLogout: () => void;
}

// 使用新的 Props 类型
const MainLayout: React.FC<MainLayoutProps> = ({ onLogout }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation(); // 获取当前位置

  // 添加用户信息状态
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  // 页面加载时读取用户信息
  useEffect(() => {
    try {
      // 先检查是否存在token，如果无token，则直接认为用户未登录（通常重定向会处理）
      const token = localStorage.getItem("authToken");
      if (!token) {
        console.warn("未检测到认证令牌，用户可能未登录");
        return;
      }

      const userInfoStr = localStorage.getItem("userInfo");
      console.log("localStorage中的用户信息原始字符串:", userInfoStr);

      if (userInfoStr) {
        const parsedUserInfo = JSON.parse(userInfoStr);
        console.log("解析后的用户信息:", parsedUserInfo);
        setUserInfo(parsedUserInfo);

        // 打印具体的字段检查
        console.log("用户ID:", parsedUserInfo.id);
        console.log("用户名:", parsedUserInfo.username);
        console.log("昵称:", parsedUserInfo.nickname);
      } else {
        console.warn("localStorage 中没有用户信息，但检测到token");

        // 也许token存在但用户信息不存在，可以尝试获取用户信息
        // TODO: 可以调用获取用户信息的API
      }
    } catch (error) {
      console.error("解析用户信息时出错:", error);
    }
  }, []);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(
          `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
        );
      });
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  };

  const handleUserMenuClick: MenuProps["onClick"] = ({ key }) => {
    console.log("User menu clicked with key:", key);
    if (key === "logout") {
      console.log("执行退出登录...");
      // 调用从 App 传来的 handleLogout 函数
      onLogout();
      // 不再需要手动移除 token 或 navigate
      // localStorage.removeItem('authToken');
      // navigate('/login', { replace: true });
    } else {
      console.log(`点击了用户菜单: ${key}`);
      // navigate(`/user/${key}`);
    }
  };

  const userMenu = <Menu onClick={handleUserMenuClick} items={userMenuItems} />;

  const handleMenuClick: MenuProps["onClick"] = ({ key }) => {
    navigate(key as string);
  };

  const selectedKeys = [location.pathname];

  // 计算展开的父菜单 keys
  const getOpenKeys = (pathname: string): string[] => {
    for (const item of menuItems) {
      // 检查 item 是否存在且有 children 属性
      if (item && "children" in item && item.children) {
        // 检查 children 中是否有匹配 pathname 的 key
        if (item.children.some((child) => child && child.key === pathname)) {
          // 返回父菜单的 key
          if (item.key) {
            return [item.key.toString()];
          }
        }
      }
    }
    return []; // 如果没找到匹配项，返回空数组
  };
  const defaultOpenKeys = getOpenKeys(location.pathname);

  // --- 面包屑动态生成逻辑 ---
  const pathSnippets = location.pathname.split("/").filter((i) => i);
  const breadcrumbNameMap: Record<string, string> = {};

  const buildBreadcrumbMap = (items: readonly MenuItem[] | undefined) => {
    if (!items) return;
    items.forEach((item) => {
      if (item && "key" in item && item.key && "label" in item && item.label) {
        breadcrumbNameMap[item.key as string] = item.label as string;
      }
      if (item && "children" in item && Array.isArray(item.children)) {
        buildBreadcrumbMap(item.children);
      }
    });
  };
  buildBreadcrumbMap(menuItems);

  const breadcrumbItems = [
    // 固定首页链接 - 将 onClick 和 style 应用到内部 span
    <Breadcrumb.Item key="home">
      <span style={{ cursor: "pointer" }} onClick={() => navigate("/")}>
        首页
      </span>
    </Breadcrumb.Item>,
    ...pathSnippets
      .map((_, index) => {
        const url = `/${pathSnippets.slice(0, index + 1).join("/")}`;
        const name = breadcrumbNameMap[url];
        const isLast = index === pathSnippets.length - 1;
        return name ? (
          <Breadcrumb.Item key={url}>
            {isLast ? (
              name // 最后一个片段只显示文本
            ) : (
              // 非最后一个片段，添加可点击的 span
              <span style={{ cursor: "pointer" }} onClick={() => navigate(url)}>
                {name}
              </span>
            )}
          </Breadcrumb.Item>
        ) : null;
      })
      .filter((item) => item !== null),
  ];
  // --- 面包屑结束 ---

  return (
    <Layout className={styles.mainLayout}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={220}
        theme="dark"
      >
        <div className={styles.logoContainer}>
          <img
            src="./logo.116606f2.png"
            alt="斑马物巢"
            className={styles.logoIcon}
            style={{ height: "28px", objectFit: "contain" }}
          />
          {!collapsed && <span className={styles.logoText}>斑马物巢</span>}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={selectedKeys}
          defaultOpenKeys={defaultOpenKeys}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <Layout className={styles.siteLayout}>
        <Header className={styles.siteHeader}>
          <div className={styles.headerLeft}>
            {React.createElement(
              collapsed ? MenuUnfoldOutlined : MenuFoldOutlined,
              {
                className: styles.trigger,
                onClick: toggleCollapsed,
              }
            )}
            <Breadcrumb style={{ marginLeft: "16px" }}>
              {breadcrumbItems}
            </Breadcrumb>
          </div>
          <div className={styles.headerRight}>
            <Space size="middle">
              <Tooltip title="消息通知">
                <Badge count={5} size="small">
                  <BellOutlined className={styles.actionIcon} />
                </Badge>
              </Tooltip>
              <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
                {isFullscreen ? (
                  <FullscreenExitOutlined
                    className={styles.actionIcon}
                    onClick={toggleFullscreen}
                  />
                ) : (
                  <FullscreenOutlined
                    className={styles.actionIcon}
                    onClick={toggleFullscreen}
                  />
                )}
              </Tooltip>
              <Tooltip title="主题设置">
                <SettingOutlined className={styles.actionIcon} />
              </Tooltip>
              <Dropdown overlay={userMenu} placement="bottomRight">
                <Space className={styles.userInfo}>
                  <Avatar
                    size="small"
                    icon={<UserOutlined />}
                    src={userInfo?.avatar}
                  />
                  <span>
                    {/* 尝试多种可能的字段名 */}
                    {userInfo
                      ? userInfo.nickname || userInfo.username || "未知用户"
                      : "未登录"}
                  </span>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>
        <Content className={styles.siteContent}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
