import React, { FC } from "react";
import {
  Table,
  Space,
  Button,
  Dropdown,
  MenuProps,
  Tooltip,
  Tag,
  Flex,
  Timeline,
  Typography,
} from "antd";
import {
  EyeOutlined,
  PrinterOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  MoreOutlined,
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  MailOutlined,
  DollarCircleOutlined,
} from "@ant-design/icons";
import type { TablePaginationConfig } from "antd/es/table";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  Manifest,
  MANIFEST_STATUS_MAP,
  ManifestStatus,
} from "../../services/manifestService";
import CopyableText from "../common/CopyableText";
import "../../styles/components/ManifestStyles.css";

const { Text } = Typography;

// 设置是否使用地址文本自动换行样式
const USE_ADDRESS_WRAP = true; // 保持为 true 以启用自动换行

interface ManifestTableProps {
  data: Manifest[];
  loading: boolean;
  total: number;
  current: number;
  pageSize: number;
  selectedRowKeys?: React.Key[];
  onSelectChange?: (selectedRowKeys: React.Key[]) => void;
  onPageChange: (page: number, pageSize: number) => void;
  onViewDetail: (id: number) => void;
  onPrint?: (id: number) => void;
  onUpdateTracking?: (id: number) => void;
  onMarkException?: (id: number) => void;
  onAdjustFee?: (id: number) => void; // 添加调整费用的回调函数
}

const ManifestTable: FC<ManifestTableProps> = ({
  data,
  loading,
  total,
  current,
  pageSize,
  selectedRowKeys,
  onSelectChange,
  onPageChange,
  onViewDetail,
  onPrint,
  onUpdateTracking,
  onMarkException,
  onAdjustFee,
}) => {
  // 确保data始终为数组，即使传入undefined或null
  const safeData = Array.isArray(data) ? data : [];

  // 表格分页配置
  const paginationProps: TablePaginationConfig = {
    current,
    pageSize,
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条记录`,
    onChange: onPageChange,
    pageSizeOptions: ["10", "20", "50", "100"],
    responsive: true,
  };

  // 获取状态标签颜色
  const getStatusTagColor = (status: ManifestStatus): string => {
    switch (status) {
      case ManifestStatus.PENDING_AUDIT:
        return "default";
      case ManifestStatus.FORECASTED:
        return "blue";
      case ManifestStatus.PICKED_UP:
        return "orange";
      case ManifestStatus.SHIPPED:
        return "green";
      case ManifestStatus.DELIVERED:
        return "purple";
      default:
        return "default";
    }
  };

  // 生成更多操作菜单
  const getMoreActionMenu = (record: Manifest): MenuProps => {
    const items: MenuProps["items"] = [];

    if (onPrint) {
      items.push({
        key: "print",
        label: "打印面单",
        icon: <PrinterOutlined />,
        onClick: () => onPrint(record.id),
      });
    }

    if (onUpdateTracking && record.status === ManifestStatus.SHIPPED) {
      items.push({
        key: "update",
        label: "更新状态",
        icon: <CheckCircleOutlined />,
        onClick: () => onUpdateTracking(record.id),
      });
    }

    if (onAdjustFee) {
      items.push({
        key: "adjustFee",
        label: "调整费用",
        icon: <DollarCircleOutlined />,
        onClick: () => onAdjustFee(record.id),
      });
    }

    if (onMarkException) {
      items.push({
        key: "exception",
        label: "标记异常",
        icon: <ExclamationCircleOutlined />,
        danger: true,
        onClick: () => onMarkException(record.id),
      });
    }

    return {
      items,
    };
  };

  // 定义表格列
  const columns: ColumnsType<Manifest> = [
    {
      title: "序号",
      key: "index",
      width: 50,
      render: (_, __, index) => (current - 1) * pageSize + index + 1,
    },
    {
      title: "单号详情",
      key: "orderNumbers",
      width: 200,
      render: (_: unknown, record: Manifest) => (
        <div style={{ display: "flex", flexDirection: "column", gap: "2px" }}>
          {record.expressNumber && (
            <Flex align="center">
              <Text className="order-label">运单:</Text>
              <CopyableText
                text={record.expressNumber}
                tooltip="复制运单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.orderNumber && (
            <Flex align="center">
              <Text className="order-label">商家:</Text>
              <CopyableText
                text={record.orderNumber}
                tooltip="复制商家订单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.orderNo && (
            <Flex align="center">
              <Text className="order-label">系统:</Text>
              <CopyableText
                text={record.orderNo}
                tooltip="复制系统订单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.transferredTrackingNumber && (
            <Flex align="center">
              <Text className="order-label">转单:</Text>
              <CopyableText
                text={record.transferredTrackingNumber}
                tooltip="复制转单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
        </div>
      ),
    },
    {
      title: "收件人信息",
      key: "receiver",
      width: 220,
      render: (_: unknown, record: Manifest) => (
        <div className="receiver-info">
          <Flex align="center" gap={4}>
            <UserOutlined style={{ color: "#555" }} />
            <span style={{ fontWeight: "bold" }}>{record.receiverName}</span>
          </Flex>

          <Flex align="center" gap={4}>
            <PhoneOutlined style={{ color: "#1890ff" }} />
            <CopyableText text={record.receiverPhone} tooltip="复制电话号码" />
          </Flex>

          {record.receiverZipCode && (
            <Flex align="center" gap={4}>
              <MailOutlined style={{ color: "#767676" }} />
              <CopyableText text={record.receiverZipCode} tooltip="复制邮编" />
            </Flex>
          )}

          <Flex align="center" gap={4}>
            <EnvironmentOutlined style={{ color: "#ff4d4f" }} />
            <div style={{ flex: 1, minWidth: 0 }}>
              <Tooltip title={record.receiverAddress} placement="topLeft">
                <div
                  className={
                    USE_ADDRESS_WRAP ? "address-text-wrap" : "address-text"
                  }
                >
                  {record.receiverAddress}
                </div>
              </Tooltip>
            </div>
          </Flex>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: ManifestStatus) => {
        const statusInfo = MANIFEST_STATUS_MAP[status] || {
          label: "未知",
          color: "default",
        };
        return <Tag color={getStatusTagColor(status)}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: "时间节点",
      key: "timelineEvents",
      width: 140, // 宽度设为较小的140px
      render: (_, record: Manifest) => {
        // 创建时间节点数组，包含所有可能的时间
        const timeItems = [];

        // 预报时间
        if (record.createTime) {
          timeItems.push({
            label: "预报时间",
            time: record.createTime,
            color: "blue",
          });
        }

        // 揽件时间
        if (record.pickUpTime) {
          timeItems.push({
            label: "揽件时间",
            time: record.pickUpTime,
            color: "orange",
          });
        }

        // 发货时间
        if (record.shipmentTime) {
          timeItems.push({
            label: "发货时间",
            time: record.shipmentTime,
            color: "green",
          });
        }

        // 送达时间
        if (record.deliveredTime) {
          timeItems.push({
            label: "送达时间",
            time: record.deliveredTime,
            color: "purple",
          });
        }

        // 如果没有任何时间，显示暂无数据
        if (timeItems.length === 0) {
          return <span className="text-muted">暂无数据</span>;
        }

        // 渲染时间线
        return (
          <Timeline
            className="compact-timeline"
            items={timeItems.map((item) => ({
              color: item.color,
              children: (
                <div className="timeline-item">
                  <div className="timeline-label">{item.label}</div>
                  <div className="timeline-time">
                    {dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                </div>
              ),
            }))}
          />
        );
      },
    },
    {
      title: "客户",
      dataIndex: "userNickname",
      key: "customer",
      width: 120,
    },
    {
      title: "操作",
      key: "action",
      width: 50,
      fixed: "right" as const,
      render: (_: unknown, record: Manifest) => {
        const menu = getMoreActionMenu(record);

        return (
          <Space size="small" className="action-buttons">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewDetail(record.id)}
            >
              详情
            </Button>

            {menu.items && menu.items.length > 0 && (
              <Dropdown menu={menu} placement="bottomRight">
                <Button type="text" size="small" icon={<MoreOutlined />} />
              </Dropdown>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <Table
      rowKey="id"
      dataSource={safeData}
      columns={columns}
      pagination={paginationProps}
      loading={loading}
      scroll={{ x: "max-content" }}
      rowSelection={
        onSelectChange
          ? {
              selectedRowKeys,
              onChange: onSelectChange,
            }
          : undefined
      }
      size="middle"
      bordered
      style={{ border: "1px solid #f0f0f0" }}
      rowClassName={(record, index) => {
        const classes = [];
        if (index % 2 === 1) {
          classes.push("zebra-stripe");
        }
        if (
          record.transferredTrackingNumber &&
          record.transferredTrackingNumber.trim() !== ""
        ) {
          classes.push("has-transferred-tracking-number");
        }
        return classes.join(" ");
      }}
    />
  );
};

export default ManifestTable;
