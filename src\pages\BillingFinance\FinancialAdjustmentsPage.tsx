import React, { useState, useEffect, useCallback } from "react";
import {
  Button,
  Table,
  Space,
  DatePicker,
  Form,
  Input,
  Select,
  message,
  Modal,
  Tag,
  Dropdown,
  Card,
  Descriptions,
  Image,
  type MenuProps,
  type TablePaginationConfig,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  MoreOutlined,
  DeleteOutlined,
  PayCircleOutlined,
  RollbackOutlined,
  PlusCircleOutlined,
} from "@ant-design/icons";
import { apiClient } from "../../services/apiClient";
import { getApiPath } from "../../services/apiPaths";
import { STANDARD_ADJUSTMENT_TYPES } from "../../services/financialService";
import dayjs from "dayjs";

import {
  CompensationModal,
  ReassignmentModal,
  DestructionModal,
  ReturnModal,
  ManifestSelectorModal,
  BatchAddModal,
} from "../../components/FinancialAdjustment/FinancialAdjustmentModals";
import CustomerSelector from "../../components/common/CustomerSelector";
import ManifestDetailDrawer from "../../components/manifest/ManifestDetailDrawer";

const { RangePicker } = DatePicker;
const { Option } = Select;

// 运单基础信息接口（与模态框组件中的类型一致）
interface ManifestBasicInfo {
  manifestId: number;
  trackingNumber: string;
  orderNumber: string;
  customerAccountId: number;
  customerNickname?: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  receiverZipCode: string;
  createTime: string;
  cost?: number;
  overLengthSurcharge?: number;
  remoteAreaSurcharge?: number;
}

// 费用调整记录项接口
interface FinancialAdjustmentItem {
  id: number;
  manifestId: number;
  trackingNumber: string; // 运单号
  adjustmentType: string;
  description: string;
  additionalDetails?: {
    // 赔偿相关详情
    cargo_value?: number;
    freight_deduction_percentage?: number;
    is_freight_deduction?: boolean;
    is_value_compensation?: boolean;
    total_freight_deduction_amount?: number;
    total_value_compensation_amount?: number;
    value_compensation_percentage?: number;
    proof_of_value_image_urls?: string[]; // 货值证明图片URLs
    // 再派相关详情
    reassignmentNumber?: string;
  };
  amount: number;
  currency: string;
  effectiveDate: string;
  customerAccountId: number;
  customerNickname?: string; // 客户昵称
  isVoid: boolean;
  voidReason?: string;
  voidedBy?: number;
  voidedTime?: string;
  creatorId: number;
  creatorNickname?: string; // 创建人昵称
  createTime: string;
  updateTime: string;
}

// 查询参数接口
interface ListFinancialAdjustmentsParams {
  manifestId?: number;
  adjustmentType?: string;
  effectiveDate?: string;
  customerAccountId?: number;
  trackingNumber?: string; // 运单号/转单号
  orderBy?: string;
  isAsc?: boolean;
  page?: number;
  pageSize?: number;
  effectiveDateStart?: string; // 生效开始时间
  effectiveDateEnd?: string; // 生效结束时间
}

// 响应数据接口
interface ListFinancialAdjustmentsResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: {
    items: FinancialAdjustmentItem[];
    total: number;
    page: number;
    pageSize: number;
  };
}

const FinancialAdjustmentsPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<FinancialAdjustmentItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchParams, setSearchParams] =
    useState<ListFinancialAdjustmentsParams>({});
  const [form] = Form.useForm();

  // 模态框状态管理
  const [manifestSelectorVisible, setManifestSelectorVisible] =
    useState<boolean>(false);
  const [compensationModalVisible, setCompensationModalVisible] =
    useState<boolean>(false);
  const [reassignmentModalVisible, setReassignmentModalVisible] =
    useState<boolean>(false);
  const [destructionModalVisible, setDestructionModalVisible] =
    useState<boolean>(false);
  const [returnModalVisible, setReturnModalVisible] = useState<boolean>(false);
  const [batchAddModalVisible, setBatchAddModalVisible] =
    useState<boolean>(false);
  const [selectedManifestInfo, setSelectedManifestInfo] =
    useState<ManifestBasicInfo | null>(null);
  const [pendingModalType, setPendingModalType] = useState<string>("");

  // ManifestDetailDrawer状态管理
  const [manifestDetailDrawerVisible, setManifestDetailDrawerVisible] =
    useState<boolean>(false);
  const [selectedManifestId, setSelectedManifestId] = useState<number | null>(
    null
  );

  // 详情和作废模态框状态
  const [detailsModalVisible, setDetailsModalVisible] =
    useState<boolean>(false);
  const [voidModalVisible, setVoidModalVisible] = useState<boolean>(false);
  const [selectedAdjustment, setSelectedAdjustment] =
    useState<FinancialAdjustmentItem | null>(null);
  const [voidForm] = Form.useForm();
  const [submittingVoid, setSubmittingVoid] = useState<boolean>(false);

  // 加载数据
  const loadData = useCallback(
    async (params: ListFinancialAdjustmentsParams = {}) => {
      try {
        setLoading(true);

        // 处理日期范围
        const queryParams: Record<
          string,
          string | number | boolean | undefined
        > = { ...params };

        // 设置生效日期范围参数
        if (params.effectiveDateStart) {
          queryParams.effectiveDateStart = params.effectiveDateStart;
        }
        if (params.effectiveDateEnd) {
          queryParams.effectiveDateEnd = params.effectiveDateEnd;
        }

        const response = await apiClient.get<ListFinancialAdjustmentsResponse>(
          getApiPath("/financial-adjustments"),
          {
            params: queryParams,
            paramsSerializer: (params) => {
              // 自定义参数序列化，处理数组等特殊情况
              return Object.entries(params)
                .filter(([, value]) => value !== undefined && value !== "")
                .map(([key, value]) => {
                  if (Array.isArray(value)) {
                    return `${key}=${value.join(",")}`;
                  }
                  return `${key}=${value}`;
                })
                .join("&");
            },
          }
        );

        if (response.data.success) {
          setData(response.data.data.items);
          setTotal(response.data.data.total);
          setCurrent(response.data.data.page);
          setPageSize(response.data.data.pageSize);
        } else {
          message.error(response.data.errorMessage || "获取费用调整列表失败");
        }
      } catch (error) {
        console.error("获取费用调整列表失败:", error);
        message.error("获取费用调整列表失败，请稍后重试");
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // 首次加载数据
  useEffect(() => {
    loadData({ page: 1, pageSize: 10 });
  }, [loadData]);

  // 处理表格分页变化
  const handleTableChange = (pagination: TablePaginationConfig) => {
    const { current, pageSize } = pagination;
    setCurrent(current || 1);
    setPageSize(pageSize || 10);

    loadData({
      ...searchParams,
      page: current,
      pageSize,
    });
  };

  // 处理搜索
  const handleSearch = (values: Record<string, unknown>) => {
    const params: ListFinancialAdjustmentsParams = {
      ...values,
      page: 1, // 搜索时重置到第一页
      pageSize,
    };

    // 处理日期范围
    if (
      values.effectiveDate &&
      Array.isArray(values.effectiveDate) &&
      values.effectiveDate.length === 2
    ) {
      params.effectiveDateStart = dayjs(values.effectiveDate[0]).format(
        "YYYY-MM-DD"
      );
      params.effectiveDateEnd = dayjs(values.effectiveDate[1]).format(
        "YYYY-MM-DD"
      );
      delete params.effectiveDate;
    }

    setSearchParams(params);
    loadData(params);
  };

  // 重置搜索条件
  const handleReset = () => {
    form.resetFields();
    const params = { page: 1, pageSize };
    setSearchParams(params);
    loadData(params);
  };

  // 生成更多操作菜单
  const getMoreActionMenu = (record: FinancialAdjustmentItem): MenuProps => {
    const items: MenuProps["items"] = [];

    if (!record.isVoid) {
      items.push({
        key: "void",
        label: "作废",
        danger: true,
        icon: <DeleteOutlined />,
        onClick: () => showVoidModal(record),
      });
    }

    return { items };
  };

  // 表格列定义
  const columns = [
    {
      title: "序号",
      key: "index",
      width: 60,
      render: (_: unknown, __: unknown, index: number) =>
        (current - 1) * pageSize + index + 1,
    },
    {
      title: "运单号",
      dataIndex: "trackingNumber",
      key: "trackingNumber",
      width: 150,
    },
    {
      title: "附加信息",
      key: "additionalInfo",
      width: 180,
      render: (_: unknown, record: FinancialAdjustmentItem) => {
        const { additionalDetails, adjustmentType } = record;

        if (!additionalDetails) return "-";

        // 显示再派单号
        if (
          adjustmentType === "REASSIGNMENT" &&
          additionalDetails.reassignmentNumber
        ) {
          return `再派单号: ${additionalDetails.reassignmentNumber}`;
        }

        // 显示赔偿详情
        if (adjustmentType === "COMPENSATION") {
          const details = [];
          if (additionalDetails.cargo_value) {
            details.push(`货值: ¥${additionalDetails.cargo_value}`);
          }
          if (additionalDetails.is_freight_deduction) {
            details.push(
              `运费扣除: ${additionalDetails.freight_deduction_percentage}%`
            );
          }
          if (additionalDetails.is_value_compensation) {
            details.push(
              `货值赔偿: ${additionalDetails.value_compensation_percentage}%`
            );
          }
          return details.length > 0 ? details.join(", ") : "-";
        }

        return "-";
      },
    },
    {
      title: "调整类型",
      dataIndex: "adjustmentType",
      key: "adjustmentType",
      width: 120,
      render: (type: string) => {
        // 使用标准调整类型显示名称
        if (type === "COMPENSATION") return "赔偿";
        if (type === "REASSIGNMENT") return "再派";
        if (type === "DESTRUCTION") return "销毁";
        if (type === "RETURN") return "退回";
        return type;
      },
    },
    {
      title: "调整金额",
      dataIndex: "amount",
      key: "amount",
      width: 150,
      render: (amount: number, record: FinancialAdjustmentItem) => {
        const { additionalDetails, adjustmentType } = record;
        const mainAmount = (
          <div
            style={{
              color: amount >= 0 ? "#52c41a" : "#ff4d4f",
              fontWeight: "bold",
            }}
          >
            {record.currency} {amount >= 0 ? "+" : ""}
            {amount.toFixed(2)}
          </div>
        );

        // 显示赔偿明细
        if (adjustmentType === "COMPENSATION" && additionalDetails) {
          const details = [];
          if (additionalDetails.total_freight_deduction_amount) {
            details.push(
              <div key="freight" style={{ fontSize: "12px", color: "#666" }}>
                运费扣除:{" "}
                {additionalDetails.total_freight_deduction_amount.toFixed(2)}
              </div>
            );
          }
          if (additionalDetails.total_value_compensation_amount) {
            details.push(
              <div key="value" style={{ fontSize: "12px", color: "#666" }}>
                货值赔偿:{" "}
                {additionalDetails.total_value_compensation_amount.toFixed(2)}
              </div>
            );
          }

          return (
            <div>
              {mainAmount}
              {details}
            </div>
          );
        }

        return mainAmount;
      },
    },
    {
      title: "客户",
      dataIndex: "customerNickname",
      key: "customerNickname",
      width: 120,
      render: (text: string, record: FinancialAdjustmentItem) =>
        text || `ID: ${record.customerAccountId}`,
    },
    {
      title: "生效日期",
      dataIndex: "effectiveDate",
      key: "effectiveDate",
      width: 150,
      render: (text: string) => dayjs(text).format("YYYY-MM-DD"),
    },
    {
      title: "状态",
      dataIndex: "isVoid",
      key: "status",
      width: 100,
      render: (isVoid: boolean) => (
        <Tag color={isVoid ? "red" : "green"}>
          {isVoid ? "已作废" : "已生效"}
        </Tag>
      ),
    },
    {
      title: "创建人",
      dataIndex: "creatorNickname",
      key: "creatorNickname",
      width: 120,
      render: (text: string, record: FinancialAdjustmentItem) =>
        text || `ID: ${record.creatorId}`,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 180,
      render: (text: string) => dayjs(text).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      fixed: "right" as const,
      render: (_: unknown, record: FinancialAdjustmentItem) => {
        const menu = getMoreActionMenu(record);

        return (
          <Space
            size="small"
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            >
              详情
            </Button>

            {menu.items && menu.items.length > 0 && (
              <Dropdown menu={menu} placement="bottomRight" trigger={["click"]}>
                <Button
                  type="text"
                  size="small"
                  icon={<MoreOutlined />}
                  style={{
                    padding: "4px 8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                />
              </Dropdown>
            )}
          </Space>
        );
      },
    },
  ];

  // 查看详情 - 修改为使用ManifestDetailDrawer
  const handleViewDetail = (record: FinancialAdjustmentItem) => {
    setSelectedManifestId(record.manifestId);
    setManifestDetailDrawerVisible(true);
  };

  // 关闭ManifestDetailDrawer
  const handleManifestDetailDrawerClose = () => {
    setManifestDetailDrawerVisible(false);
    setSelectedManifestId(null);
  };

  // 关闭详情模态框
  const handleDetailsModalClose = () => {
    setDetailsModalVisible(false);
    setSelectedAdjustment(null);
  };

  // 显示作废模态框
  const showVoidModal = (record: FinancialAdjustmentItem) => {
    setSelectedAdjustment(record);
    voidForm.resetFields();
    setVoidModalVisible(true);
  };

  // 关闭作废模态框
  const handleVoidModalCancel = () => {
    setVoidModalVisible(false);
    setSelectedAdjustment(null);
    voidForm.resetFields();
  };

  // 提交作废
  const handleVoidSubmit = async () => {
    if (!selectedAdjustment) {
      message.error("未选择要作废的费用调整记录");
      return;
    }

    try {
      const values = await voidForm.validateFields();
      setSubmittingVoid(true);

      // 调用作废API
      await apiClient.post(getApiPath("/financial-adjustments/void"), {
        id: selectedAdjustment.id,
        voidReason: values.reason,
      });

      message.success("费用调整单已作废");
      setVoidModalVisible(false);
      voidForm.resetFields();
      setSelectedAdjustment(null);

      // 刷新数据
      loadData({ ...searchParams, page: current, pageSize });
    } catch (error) {
      console.error("作废费用调整单失败:", error);
      message.error("作废失败，请稍后重试");
    } finally {
      setSubmittingVoid(false);
    }
  };

  // 处理模态框选择运单后的回调
  const handleManifestSelected = (manifestInfo: ManifestBasicInfo) => {
    setSelectedManifestInfo(manifestInfo);
    setManifestSelectorVisible(false);

    // 根据待处理的模态框类型打开对应模态框
    switch (pendingModalType) {
      case "compensation":
        setCompensationModalVisible(true);
        break;
      case "reassignment":
        setReassignmentModalVisible(true);
        break;
      case "destruction":
        setDestructionModalVisible(true);
        break;
      case "return":
        setReturnModalVisible(true);
        break;
    }
    setPendingModalType("");
  };

  // 处理费用调整成功后的回调
  const handleAdjustmentSuccess = () => {
    // 刷新数据列表
    loadData({ ...searchParams, page: current, pageSize });

    // 关闭所有模态框
    setCompensationModalVisible(false);
    setReassignmentModalVisible(false);
    setDestructionModalVisible(false);
    setReturnModalVisible(false);
    setSelectedManifestInfo(null);
  };

  // 显示运单选择模态框
  const showManifestSelector = (modalType: string) => {
    setPendingModalType(modalType);
    setManifestSelectorVisible(true);
  };

  // 直接打开各种调整模态框的处理函数
  const handleShowCompensation = () => showManifestSelector("compensation");
  const handleShowReassignment = () => showManifestSelector("reassignment");
  const handleShowDestruction = () => showManifestSelector("destruction");
  const handleShowReturn = () => showManifestSelector("return");

  /**
   * 打开批量添加模态框
   */
  const handleShowBatchAdd = () => {
    setBatchAddModalVisible(true);
  };

  /**
   * 关闭批量添加模态框
   */
  const handleCloseBatchAdd = () => {
    setBatchAddModalVisible(false);
  };

  /**
   * 批量添加成功回调
   */
  const handleBatchAddSuccess = () => {
    // 刷新数据列表
    loadData({ ...searchParams, page: current, pageSize });
    setBatchAddModalVisible(false);
  };

  return (
    <div className="financial-adjustments-page">
      <Card title="费用调整" bordered={false}>
        {/* 搜索表单 */}
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item label="单号" name="trackingNumber">
            <Input placeholder="请输入运单号/转单号" />
          </Form.Item>
          <Form.Item name="adjustmentType" label="调整类型">
            <Select
              placeholder="请选择调整类型"
              style={{ width: 180 }}
              allowClear
            >
              <Option value={STANDARD_ADJUSTMENT_TYPES.COMPENSATION.value}>
                {STANDARD_ADJUSTMENT_TYPES.COMPENSATION.label}
              </Option>
              <Option value={STANDARD_ADJUSTMENT_TYPES.REASSIGNMENT.value}>
                {STANDARD_ADJUSTMENT_TYPES.REASSIGNMENT.label}
              </Option>
              <Option value={STANDARD_ADJUSTMENT_TYPES.DESTRUCTION.value}>
                {STANDARD_ADJUSTMENT_TYPES.DESTRUCTION.label}
              </Option>
              <Option value={STANDARD_ADJUSTMENT_TYPES.RETURN.value}>
                {STANDARD_ADJUSTMENT_TYPES.RETURN.label}
              </Option>
            </Select>
          </Form.Item>

          <Form.Item name="effectiveDate" label="生效日期">
            <RangePicker style={{ width: 240 }} />
          </Form.Item>

          <Form.Item name="customerAccountId" label="客户筛选">
            <CustomerSelector
              placeholder="输入客户昵称/用户名搜索"
              style={{ width: 200 }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
              >
                查询
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space size="middle">
            <Button
              type="primary"
              icon={<PayCircleOutlined />}
              onClick={handleShowCompensation}
              style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
            >
              赔偿
            </Button>
            <Button
              type="primary"
              icon={<PayCircleOutlined />}
              onClick={handleShowReassignment}
              style={{ backgroundColor: "#1890ff", borderColor: "#1890ff" }}
            >
              改派
            </Button>
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              onClick={handleShowDestruction}
            >
              销毁
            </Button>
            <Button
              type="primary"
              icon={<RollbackOutlined />}
              onClick={handleShowReturn}
              style={{ backgroundColor: "#faad14", borderColor: "#faad14" }}
            >
              退回
            </Button>
            <Button
              type="primary"
              icon={<PlusCircleOutlined />}
              onClick={handleShowBatchAdd}
            >
              批量添加
            </Button>
          </Space>
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1420 }}
          bordered
        />
      </Card>

      {/* 模态框组件 */}
      <ManifestSelectorModal
        visible={manifestSelectorVisible}
        onCancel={() => {
          setManifestSelectorVisible(false);
          setPendingModalType("");
        }}
        onSelect={handleManifestSelected}
      />

      <CompensationModal
        visible={compensationModalVisible}
        onCancel={() => {
          setCompensationModalVisible(false);
          setSelectedManifestInfo(null);
        }}
        onSuccess={handleAdjustmentSuccess}
        manifestInfo={selectedManifestInfo || undefined}
      />

      <ReassignmentModal
        visible={reassignmentModalVisible}
        onCancel={() => {
          setReassignmentModalVisible(false);
          setSelectedManifestInfo(null);
        }}
        onSuccess={handleAdjustmentSuccess}
        manifestInfo={selectedManifestInfo || undefined}
      />

      <DestructionModal
        visible={destructionModalVisible}
        onCancel={() => {
          setDestructionModalVisible(false);
          setSelectedManifestInfo(null);
        }}
        onSuccess={handleAdjustmentSuccess}
        manifestInfo={selectedManifestInfo || undefined}
      />

      <ReturnModal
        visible={returnModalVisible}
        onCancel={() => {
          setReturnModalVisible(false);
          setSelectedManifestInfo(null);
        }}
        onSuccess={handleAdjustmentSuccess}
        manifestInfo={selectedManifestInfo || undefined}
      />

      {/* 运单详情抽屉 */}
      <ManifestDetailDrawer
        manifestId={selectedManifestId}
        visible={manifestDetailDrawerVisible}
        onClose={handleManifestDetailDrawerClose}
        scrollToAdjustments={true}
      />

      {/* 详情模态框 */}
      <Modal
        title="费用调整详情"
        open={detailsModalVisible}
        onCancel={handleDetailsModalClose}
        footer={[
          <Button key="close" onClick={handleDetailsModalClose}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {selectedAdjustment && (
          <div>
            <Descriptions
              column={2}
              bordered
              size="small"
              style={{ marginBottom: "16px" }}
            >
              <Descriptions.Item label="运单号">
                {selectedAdjustment.trackingNumber}
              </Descriptions.Item>
              <Descriptions.Item label="调整类型">
                {selectedAdjustment.adjustmentType === "COMPENSATION" && "赔偿"}
                {selectedAdjustment.adjustmentType === "REASSIGNMENT" && "改派"}
                {selectedAdjustment.adjustmentType === "DESTRUCTION" && "销毁"}
                {selectedAdjustment.adjustmentType === "RETURN" && "退回"}
              </Descriptions.Item>
              <Descriptions.Item label="调整金额">
                <span
                  style={{
                    color:
                      selectedAdjustment.amount >= 0 ? "#52c41a" : "#ff4d4f",
                    fontWeight: "bold",
                  }}
                >
                  {selectedAdjustment.currency}{" "}
                  {selectedAdjustment.amount >= 0 ? "+" : ""}
                  {selectedAdjustment.amount.toFixed(2)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="生效日期">
                {dayjs(selectedAdjustment.effectiveDate).format("YYYY-MM-DD")}
              </Descriptions.Item>
              <Descriptions.Item label="客户">
                {selectedAdjustment.customerNickname ||
                  `ID: ${selectedAdjustment.customerAccountId}`}
              </Descriptions.Item>
              <Descriptions.Item label="创建人">
                {selectedAdjustment.creatorNickname ||
                  `ID: ${selectedAdjustment.creatorId}`}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(selectedAdjustment.createTime).format(
                  "YYYY-MM-DD HH:mm:ss"
                )}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <span
                  style={{
                    color: selectedAdjustment.isVoid ? "#ff4d4f" : "#52c41a",
                  }}
                >
                  {selectedAdjustment.isVoid ? "已作废" : "已生效"}
                </span>
              </Descriptions.Item>
            </Descriptions>

            <Descriptions
              column={1}
              bordered
              size="small"
              style={{ marginBottom: "16px" }}
            >
              <Descriptions.Item label="调整说明">
                {selectedAdjustment.description || "-"}
              </Descriptions.Item>
            </Descriptions>

            {/* 附加信息展示 */}
            {selectedAdjustment.additionalDetails && (
              <Descriptions
                title="附加信息"
                column={2}
                bordered
                size="small"
                style={{ marginBottom: "16px" }}
              >
                {selectedAdjustment.adjustmentType === "REASSIGNMENT" &&
                  selectedAdjustment.additionalDetails.reassignmentNumber && (
                    <Descriptions.Item label="改派单号" span={2}>
                      {selectedAdjustment.additionalDetails.reassignmentNumber}
                    </Descriptions.Item>
                  )}
                {selectedAdjustment.adjustmentType === "COMPENSATION" && (
                  <>
                    {selectedAdjustment.additionalDetails.cargo_value && (
                      <Descriptions.Item label="货值">
                        ¥{selectedAdjustment.additionalDetails.cargo_value}
                      </Descriptions.Item>
                    )}
                    {selectedAdjustment.additionalDetails
                      .is_freight_deduction && (
                      <Descriptions.Item label="运费扣除比例">
                        {
                          selectedAdjustment.additionalDetails
                            .freight_deduction_percentage
                        }
                        %
                      </Descriptions.Item>
                    )}
                    {selectedAdjustment.additionalDetails
                      .is_value_compensation && (
                      <Descriptions.Item label="货值赔偿比例">
                        {
                          selectedAdjustment.additionalDetails
                            .value_compensation_percentage
                        }
                        %
                      </Descriptions.Item>
                    )}
                    {/* 货值证明图片展示 */}
                    {selectedAdjustment.additionalDetails
                      .proof_of_value_image_urls &&
                      selectedAdjustment.additionalDetails
                        .proof_of_value_image_urls.length > 0 && (
                        <Descriptions.Item label="货值证明图片" span={2}>
                          <div
                            style={{
                              display: "flex",
                              flexWrap: "wrap",
                              gap: "8px",
                            }}
                          >
                            {selectedAdjustment.additionalDetails.proof_of_value_image_urls.map(
                              (url, index) => (
                                <div
                                  key={index}
                                  style={{
                                    width: "60px",
                                    height: "60px",
                                    border: "1px solid #d9d9d9",
                                    borderRadius: "4px",
                                    overflow: "hidden",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <Image
                                    src={url}
                                    alt={`货值证明图片${index + 1}`}
                                    style={{
                                      width: "100%",
                                      height: "100%",
                                      objectFit: "cover",
                                    }}
                                    preview={{
                                      mask: (
                                        <EyeOutlined
                                          style={{ color: "#fff" }}
                                        />
                                      ),
                                    }}
                                    fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAyNEMzMS42NTY5IDI0IDMzIDI1LjM0MzEgMzMgMjdDMzMgMjguNjU2OSAzMS42NTY5IDMwIDMwIDMwQzI4LjM0MzEgMzAgMjcgMjguNjU2OSAyNyAyN0MyNyAyNS4zNDMxIDI4LjM0MzEgMjQgMzAgMjRaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik0yMSAzNkwzMCAzMEwzOSAzNkgyMVoiIGZpbGw9IiNCRkJGQkYiLz4KPHN2Zz4K"
                                  />
                                </div>
                              )
                            )}
                          </div>
                          {selectedAdjustment.additionalDetails
                            .proof_of_value_image_urls.length > 0 && (
                            <div
                              style={{
                                marginTop: "8px",
                                fontSize: "12px",
                                color: "#666",
                              }}
                            >
                              共{" "}
                              {
                                selectedAdjustment.additionalDetails
                                  .proof_of_value_image_urls.length
                              }{" "}
                              张图片，点击可放大查看
                            </div>
                          )}
                        </Descriptions.Item>
                      )}
                  </>
                )}
              </Descriptions>
            )}

            {/* 作废信息 */}
            {selectedAdjustment.isVoid && (
              <div
                style={{
                  marginTop: "16px",
                  padding: "12px",
                  border: "1px solid #ffccc7",
                  borderRadius: "4px",
                  backgroundColor: "#fff2f0",
                }}
              >
                <Descriptions
                  title="作废信息"
                  column={1}
                  bordered={false}
                  size="small"
                >
                  <Descriptions.Item label="作废原因">
                    {selectedAdjustment.voidReason || "-"}
                  </Descriptions.Item>
                  {selectedAdjustment.voidedTime && (
                    <Descriptions.Item label="作废时间">
                      {dayjs(selectedAdjustment.voidedTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    </Descriptions.Item>
                  )}
                </Descriptions>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 作废模态框 */}
      <Modal
        title="作废费用调整"
        open={voidModalVisible}
        onCancel={handleVoidModalCancel}
        onOk={handleVoidSubmit}
        confirmLoading={submittingVoid}
        maskClosable={false}
        destroyOnClose
      >
        <Form form={voidForm} layout="vertical">
          <Form.Item
            name="reason"
            label="作废原因"
            rules={[{ required: true, message: "请输入作废原因" }]}
          >
            <Input.TextArea rows={3} placeholder="请输入作废原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量添加模态框 */}
      <BatchAddModal
        visible={batchAddModalVisible}
        onCancel={handleCloseBatchAdd}
        onSuccess={handleBatchAddSuccess}
      />
    </div>
  );
};

export default FinancialAdjustmentsPage;
