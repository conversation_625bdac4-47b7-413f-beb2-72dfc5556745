import React, { useState } from "react";
import { message, Tooltip } from "antd";
import { CopyOutlined, CheckOutlined } from "@ant-design/icons";
import "../../styles/components/ManifestStyles.css";

interface CopyableTextProps {
  text: string;
  tooltip?: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 可复制文本组件
 * 点击即可复制文本内容，并显示复制成功提示
 */
const CopyableText: React.FC<CopyableTextProps> = ({
  text,
  tooltip = "点击复制",
  className = "",
  style = {},
}) => {
  const [copied, setCopied] = useState(false);

  if (!text) return <span>-</span>;

  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发父元素的点击事件

    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      message.success("复制成功");

      // 2秒后重置复制状态
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch {
      // 如果navigator.clipboard失败，尝试使用传统方法
      try {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        // 防止滚动到底部
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand("copy");
        document.body.removeChild(textArea);

        if (successful) {
          setCopied(true);
          message.success("复制成功");
          // 2秒后重置复制状态
          setTimeout(() => {
            setCopied(false);
          }, 2000);
        } else {
          message.error("复制失败，请手动选择并复制");
        }
      } catch {
        message.error("复制失败，请手动选择并复制");
      }
    }
  };

  return (
    <Tooltip title={copied ? "已复制" : tooltip}>
      <div
        className={`copyable-text ${className}`}
        onClick={handleCopy}
        style={style}
      >
        <span className="text">{text}</span>
        {copied ? (
          <CheckOutlined className="copied-icon" />
        ) : (
          <CopyOutlined className="copy-icon" />
        )}
      </div>
    </Tooltip>
  );
};

export default CopyableText;
