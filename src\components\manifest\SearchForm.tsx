import React, {
  forwardRef,
  useImperativeHandle,
  ForwardRefRenderFunction,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  DatePicker,
  Select,
  Spin,
  Empty,
  Space,
} from "antd";
import { SearchOutlined, ReloadOutlined } from "@ant-design/icons";
import dayjs, { Dayjs } from "dayjs";
import debounce from "lodash/debounce";
import { fetchUserOptions, UserOption } from "../../services/userService";
import {
  fetchMasterBillOptions,
  MasterBillOption,
} from "../../services/masterBillService";

const { RangePicker } = DatePicker;
const { Option } = Select;

export interface SearchFormValues {
  query?: string;
  dateType?: "createTime" | "pickUpTime" | "shipmentTime" | "deliveredTime";
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  userId?: number | null;
  masterBillId?: number | null;
}

export interface SearchParams {
  query?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  pickUpTimeStart?: string;
  pickUpTimeEnd?: string;
  shipmentTimeStart?: string;
  shipmentTimeEnd?: string;
  deliveredTimeStart?: string;
  deliveredTimeEnd?: string;
  userId?: number;
  masterBillId?: number;
}

export interface SearchFormRef {
  resetForm: () => void;
  submitForm: () => void;
}

interface SearchFormProps {
  onSearch: (params: SearchParams) => void;
  loading: boolean;
  setLoading?: (loading: boolean) => void;
  buttonPosition?: "right" | "none";
}

const ITEM_PAGE_SIZE = 20;

const SearchForm: ForwardRefRenderFunction<SearchFormRef, SearchFormProps> = (
  { onSearch, loading, setLoading, buttonPosition = "right" },
  ref
) => {
  const [form] = Form.useForm();
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userLoading, setUserLoading] = useState<boolean>(false);
  const [userPage, setUserPage] = useState<number>(1);
  const [userTotal, setUserTotal] = useState<number>(0);
  const [userSearchKeyword, setUserSearchKeyword] = useState<string>("");
  const [masterBillOptions, setMasterBillOptions] = useState<
    MasterBillOption[]
  >([]);
  const [masterBillLoading, setMasterBillLoading] = useState<boolean>(false);
  const [masterBillPage, setMasterBillPage] = useState<number>(1);
  const [masterBillTotal, setMasterBillTotal] = useState<number>(0);
  const [masterBillSearchKeyword, setMasterBillSearchKeyword] =
    useState<string>("");

  const loadUserOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setUserLoading(true);
      try {
        const apiResponse = await fetchUserOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;

          if (Array.isArray(list) && typeof total === "number") {
            setUserOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setUserTotal(total);
            setUserPage(page);
          } else {
            console.error(
              "Failed to load user options: Data structure inside ApiResponse.data is invalid.",
              apiResponse
            );
            if (!append) {
              setUserOptions([]);
              setUserTotal(0);
            }
          }
        } else {
          console.error(
            "Failed to load user options:",
            apiResponse.errorMessage || "API call failed or data was null"
          );
          if (!append) {
            setUserOptions([]);
            setUserTotal(0);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching user options (network/exception):",
          error
        );
        if (!append) {
          setUserOptions([]);
          setUserTotal(0);
        }
      } finally {
        setUserLoading(false);
      }
    },
    []
  );

  const loadMasterBillOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setMasterBillLoading(true);
      try {
        const apiResponse = await fetchMasterBillOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;

          if (Array.isArray(list) && typeof total === "number") {
            setMasterBillOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setMasterBillTotal(total);
            setMasterBillPage(page);
          } else {
            console.error(
              "Failed to load master bill options: Data structure inside ApiResponse.data is invalid.",
              apiResponse
            );
            if (!append) {
              setMasterBillOptions([]);
              setMasterBillTotal(0);
            }
          }
        } else {
          console.error(
            "Failed to load master bill options:",
            apiResponse.errorMessage || "API call failed or data was null"
          );
          if (!append) {
            setMasterBillOptions([]);
            setMasterBillTotal(0);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching master bill options (network/exception):",
          error
        );
        if (!append) {
          setMasterBillOptions([]);
          setMasterBillTotal(0);
        }
      } finally {
        setMasterBillLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    loadUserOptions("", 1);
    loadMasterBillOptions("", 1);
  }, [loadUserOptions, loadMasterBillOptions]);

  const debouncedUserSearch = useCallback(
    debounce((keyword: string) => {
      console.log(
        "[SearchForm] debouncedUserSearch triggered with keyword:",
        JSON.stringify(keyword)
      );
      setUserOptions([]);
      setUserPage(1);
      setUserSearchKeyword(keyword);
      loadUserOptions(keyword, 1, false);
    }, 500),
    [loadUserOptions]
  );

  const debouncedMasterBillSearch = useCallback(
    debounce((keyword: string) => {
      console.log(
        "[SearchForm] debouncedMasterBillSearch triggered with keyword:",
        JSON.stringify(keyword)
      );
      setMasterBillOptions([]);
      setMasterBillPage(1);
      setMasterBillSearchKeyword(keyword);
      loadMasterBillOptions(keyword, 1, false);
    }, 500),
    [loadMasterBillOptions]
  );

  const handleUserPopupScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.currentTarget;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (userOptions.length < userTotal && !userLoading) {
        loadUserOptions(userSearchKeyword, userPage + 1, true);
      }
    }
  };

  const handleMasterBillPopupScroll = (
    event: React.UIEvent<HTMLDivElement>
  ) => {
    const target = event.currentTarget;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (masterBillOptions.length < masterBillTotal && !masterBillLoading) {
        loadMasterBillOptions(
          masterBillSearchKeyword,
          masterBillPage + 1,
          true
        );
      }
    }
  };

  useImperativeHandle(ref, () => ({
    resetForm: () => {
      form.resetFields();
      debouncedUserSearch("");
      debouncedMasterBillSearch("");
      onSearch({});
    },
    submitForm: () => {
      if (setLoading) {
        setLoading(true);
      }
      handleFormSubmit();
    },
  }));

  const transformFormToParams = (values: SearchFormValues): SearchParams => {
    const { query, dateType, dateRange, userId, masterBillId } = values;
    const params: SearchParams = {};

    if (query) params.query = query;
    if (userId !== undefined && userId !== null) params.userId = userId;
    if (masterBillId !== undefined && masterBillId !== null)
      params.masterBillId = masterBillId;

    if (dateRange && dateRange.length === 2) {
      const startDate = dateRange[0].format("YYYY-MM-DD");
      const endDate = dateRange[1].format("YYYY-MM-DD");
      switch (dateType) {
        case "createTime":
          params.createTimeStart = startDate;
          params.createTimeEnd = endDate;
          break;
        case "pickUpTime":
          params.pickUpTimeStart = startDate;
          params.pickUpTimeEnd = endDate;
          break;
        case "shipmentTime":
          params.shipmentTimeStart = startDate;
          params.shipmentTimeEnd = endDate;
          break;
        case "deliveredTime":
          params.deliveredTimeStart = startDate;
          params.deliveredTimeEnd = endDate;
          break;
        default:
          params.createTimeStart = startDate;
          params.createTimeEnd = endDate;
      }
    }
    return params;
  };

  const handleReset = () => {
    form.resetFields();
    debouncedUserSearch("");
    debouncedMasterBillSearch("");
    onSearch({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.value) {
      if (setLoading) setLoading(true);
      handleFormSubmit();
    }
  };

  const handleInputPressEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (setLoading) setLoading(true);
    handleFormSubmit();
  };

  const handleSelectEnter = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (setLoading) setLoading(true);
      handleFormSubmit();
    }
  };

  const handleFormSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const searchParams = transformFormToParams(values);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error("表单校验失败:", error);
        if (setLoading) setLoading(false);
      });
  };

  const handleSelectionChange = (fieldName: string, value?: number) => {
    if (value === undefined) {
      if (fieldName === "userId") debouncedUserSearch("");
      if (fieldName === "masterBillId") debouncedMasterBillSearch("");
    }

    if (setLoading) {
      setLoading(true);
    }
    form
      .validateFields()
      .then((currentValues) => {
        const searchParams = transformFormToParams(currentValues);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error(`表单校验失败 (on${fieldName}Change):`, error);
        if (setLoading) {
          setLoading(false);
        }
      });
  };

  const handleDateRangeChange = (
    dates: [Dayjs | null, Dayjs | null] | null,
    dateStrings: [string, string]
  ) => {
    if (setLoading) {
      setLoading(true);
    }
    form
      .validateFields()
      .then((currentValues) => {
        const searchParams = transformFormToParams(currentValues);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error("表单校验失败 (onDateRangeChange):", error);
        if (setLoading) {
          setLoading(false);
        }
      });
  };

  return (
    <Form
      form={form}
      layout="horizontal"
      initialValues={{ dateType: "createTime" }}
      onFinish={handleFormSubmit}
      className="search-form"
    >
      <Row gutter={[16, 16]} justify="space-between">
        <Col xs={24} sm={24} md={8} lg={7} xl={6}>
          <Form.Item name="query" label="单号搜索">
            <Input
              placeholder="运单号/商家订单号/系统订单号/转单号"
              allowClear
              onChange={handleInputChange}
              onPressEnter={handleInputPressEnter}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={24} md={8} lg={7} xl={6}>
          <Form.Item name="userId" label="客户筛选">
            <Select
              placeholder="输入客户昵称/用户名搜索"
              allowClear
              showSearch
              filterOption={false}
              onSearch={debouncedUserSearch}
              loading={userLoading}
              onPopupScroll={handleUserPopupScroll}
              onChange={(value) => handleSelectionChange("userId", value)}
              notFoundContent={
                userLoading ? (
                  <Spin size="small" />
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无数据"
                  />
                )
              }
            >
              {userOptions.map((user) => (
                <Option key={user.id} value={user.id} title={user.nickname}>
                  {user.nickname} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={24} md={8} lg={7} xl={6}>
          <Form.Item name="masterBillId" label="提单筛选">
            <Select
              placeholder="输入提单号搜索"
              allowClear
              showSearch
              filterOption={false}
              onSearch={debouncedMasterBillSearch}
              loading={masterBillLoading}
              onPopupScroll={handleMasterBillPopupScroll}
              onChange={(value) => handleSelectionChange("masterBillId", value)}
              notFoundContent={
                masterBillLoading ? (
                  <Spin size="small" />
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无数据"
                  />
                )
              }
            >
              {masterBillOptions.map((bill) => (
                <Option
                  key={bill.id}
                  value={bill.id}
                  title={bill.masterBillNumber}
                >
                  {bill.masterBillNumber}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginTop: "16px" }}>
        <Col xs={24} sm={12} md={6} lg={5} xl={4}>
          <Form.Item name="dateType" label="日期类型">
            <Select onKeyDown={handleSelectEnter}>
              <Option value="createTime">预报时间</Option>
              <Option value="pickUpTime">揽件时间</Option>
              <Option value="shipmentTime">发货时间</Option>
              <Option value="deliveredTime">送达时间</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={10} lg={9} xl={8}>
          <Form.Item name="dateRange" label="日期范围">
            <RangePicker
              style={{ width: "100%" }}
              onChange={handleDateRangeChange}
            />
          </Form.Item>
        </Col>
        {buttonPosition === "right" && (
          <Col
            xs={24}
            sm={24}
            md={8}
            lg={6}
            xl={4}
            style={{ textAlign: "right", alignSelf: "flex-start" }}
          >
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  htmlType="submit"
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        )}
      </Row>
    </Form>
  );
};

export default forwardRef(SearchForm);
