import React, { useState } from 'react';
import { Modal, Form, Input, Button, message } from 'antd';
import { createAdjustmentType } from '../../services/financialService';

interface AddAdjustmentTypeModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const AddAdjustmentTypeModal: React.FC<AddAdjustmentTypeModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      await createAdjustmentType({
        name: values.name,
        description: values.description || '',
      });
      
      message.success('添加成功');
      form.resetFields();
      onSuccess();
      onCancel();
    } catch (error) {
      if (error instanceof Error) {
        message.error(`添加失败: ${error.message}`);
      } else {
        message.error('添加失败，请重试');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="添加调整类型"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          确定
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="name"
          label="类型名称"
          rules={[{ required: true, message: '请输入类型名称' }]}
        >
          <Input placeholder="请输入类型名称" />
        </Form.Item>
        <Form.Item name="description" label="类型描述">
          <Input.TextArea placeholder="请输入类型描述（可选）" rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddAdjustmentTypeModal;
