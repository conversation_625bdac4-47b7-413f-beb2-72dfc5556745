import { apiClient, ApiResponse, ApiError } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 定义API响应中data字段的结构
interface ProblemManifestCountData {
  count: number;
}

/**
 * 获取问题舱单（问题订单）的数量
 * @returns Promise<number> 返回问题订单的数量
 * @throws ApiError 如果请求失败或响应格式不正确
 */
export const getProblemManifestCount = async (): Promise<number> => {
  try {
    const response = await apiClient.get<ApiResponse<ProblemManifestCountData>>(
      getApiPath("/problem-tickets/problem-count")
    );

    // 检查业务成功标志和数据结构
    if (
      response.data &&
      response.data.success &&
      response.data.data &&
      typeof response.data.data.count === "number"
    ) {
      return response.data.data.count;
    } else {
      // 如果响应的 success 为 false 或数据结构不符合预期，也视为一种错误
      const errorMessage =
        response.data?.errorMessage || "获取问题订单数量失败，响应格式不正确";
      const errorCode = response.data?.errorCode || -1; // 自定义一个错误码表示非标准错误
      console.error(errorMessage, response.data);
      throw new ApiError(errorMessage, errorCode);
    }
  } catch (error) {
    // apiClient 的拦截器已经处理了 AxiosError 并可能将其转换为 ApiError
    // 这里再次捕获以确保错误被正确抛出或记录
    console.error("获取问题订单数量时发生网络或服务器错误:", error);
    if (error instanceof ApiError) {
      throw error; // 如果已经是 ApiError，直接再次抛出
    }
    // 对于其他未知错误，包装成 ApiError
    throw new ApiError("获取问题订单数量时发生未知错误", 0);
  }
};
