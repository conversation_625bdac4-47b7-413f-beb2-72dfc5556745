# 表单值获取问题调试指南

## 问题描述

用户在"配置模板"页面修改了付款截止日期、货币单位、账单备注，但这些修改后的值没有传递给后端。

## 最新修复

### 实现双重获取策略

我们现在使用两种方式获取表单值：

1. **getFieldsValue()** - 获取所有表单值
2. **getFieldValue(fieldName)** - 单独获取特定字段

然后优先使用单独获取的值：

```typescript
// 单独获取基本字段
const dueDateSingle = templateForm.getFieldValue("dueDate");
const notesSingle = templateForm.getFieldValue("notes");
const currencySingle = templateForm.getFieldValue("currency");

// 多重后备策略
generateParams.dueDate =
  dueDateFromSingle || // 优先：单独获取
  dueDateFromAll || // 备选：批量获取
  basicFields.dueDate || // 备选：组件状态
  dayjs().add(30, "day").format("YYYY-MM-DD"); // 最终：计算值
```

## 测试步骤

### 1. 访问生成账单功能

- 地址：http://localhost:5179/#/billing/bills
- 点击"生成账单"按钮

### 2. 完成前两步

1. **选择时间范围** - 选择合适的时间段
2. **选择用户** - 选择有数据的用户

### 3. 关键测试：修改表单字段

在第三步"配置模板"页面：

#### 测试场景 1：修改付款截止日期

- 原始显示：2025-06-26（或其他默认值）
- **修改为**：2025-07-31
- 点击"下一步"

#### 测试场景 2：修改货币单位

- 原始显示：人民币(CNY)
- **修改为**：美元(USD) 或 欧元(EUR)
- 点击"下一步"

#### 测试场景 3：修改账单备注

- 原始显示："福州日贸通网络科技有限公司 2025 年 05 月 运费账单"
- **修改为**："这是测试备注 - 用户手动修改"
- 点击"下一步"

### 4. 查看调试信息

在第四步"确认生成"页面：

1. **打开开发者工具**

   - 按 F12 键
   - 切换到 Console 标签

2. **点击"确认生成账单"按钮**

3. **查看详细的调试输出**

## 期望的调试输出

### 关键调试信息

```console
=== 两种获取方式对比 ===
getFieldsValue方式:
  dueDate: [用户修改后的dayjs对象]
  notes: "这是测试备注 - 用户手动修改"
  currency: "USD"
getFieldValue方式:
  dueDate: [用户修改后的dayjs对象]
  notes: "这是测试备注 - 用户手动修改"
  currency: "USD"

=== 最终参数构建 ===
dueDateFromSingle: "2025-07-31"
dueDateFromAll: "2025-07-31"
notesFromSingle: "这是测试备注 - 用户手动修改"
notesFromAll: "这是测试备注 - 用户手动修改"
currencySingle: "USD"
最终dueDate: "2025-07-31"
最终notes: "这是测试备注 - 用户手动修改"
最终currency: "USD"
```

### Network 请求验证

在 **Network** 标签中查看请求参数：

```json
{
  "startTime": "2025-05-01 00:00:00",
  "endTime": "2025-05-27 00:00:00",
  "userId": 50,
  "currency": "USD",                                    // ✅ 应该是修改后的值
  "dueDate": "2025-07-31",                             // ✅ 应该是修改后的值
  "notes": "这是测试备注 - 用户手动修改",                  // ✅ 应该是修改后的值
  "generalTemplate": {...},
  "batteryTemplate": {...},
  "postBoxTemplate": {...}
}
```

## 问题诊断

### 如果两种获取方式结果不一致

**getFieldsValue 获取为空，但 getFieldValue 有值**

这表明：

- 表单字段名可能在嵌套结构中
- `getFieldsValue()` 可能受到模板字段干扰
- **解决方案**：使用单独获取的值（已实现）

### 如果两种获取方式都为空

**表单值根本没有更新**

可能原因：

1. 表单字段没有正确绑定
2. 表单更新事件没有触发
3. 表单字段名配置错误

**调试步骤**：

1. 检查表单字段的 `name` 属性
2. 确认表单控件的 `onChange` 事件
3. 手动触发表单更新

### 如果获取到值但格式不对

**例如日期为字符串而不是 dayjs 对象**

可能原因：

- 表单控件类型配置错误
- 日期控件返回格式异常

**解决方案**：

- 检查 DatePicker 的配置
- 添加格式转换逻辑

## 临时解决方案

如果调试发现根本问题，可以使用以下临时方案：

### 方案 1：强制使用页面显示值

```typescript
// 根据页面当前显示的值强制设置
generateParams.dueDate = "2025-07-31"; // 用户看到的值
generateParams.notes = "用户实际修改的备注";
generateParams.currency = "USD";
```

### 方案 2：添加表单变更监听

```typescript
// 在表单字段上添加 onChange 监听
const [currentFormValues, setCurrentFormValues] = useState({});

// 表单字段变更时更新状态
const handleFormChange = () => {
  const values = templateForm.getFieldsValue();
  setCurrentFormValues(values);
  console.log("表单值变更:", values);
};
```

### 方案 3：使用受控组件

将基本字段改为完全受控组件，不依赖表单自动绑定。

## 成功标准

✅ **修复成功的标志**：

1. 用户修改表单字段后，Console 显示修改后的值
2. Network 请求中包含用户修改后的参数值
3. 后端接收到正确的参数值

如果仍然有问题，请提供：

1. 完整的 Console 输出截图
2. Network 请求参数截图
3. 表单修改前后的对比

这样可以更精确地定位问题所在。
