/* 运费模板管理页面样式 */

.container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.pageTitle {
  margin-bottom: 24px;
  color: #1890ff;
}

.typeSelectionCard {
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.typeSelectionContent {
  width: 100%;
}

.typeSelectionRow {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.typeSelect {
  width: 300px;
  min-width: 200px;
}

.templatesCard {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emptyState {
  text-align: center;
  padding: 60px 0;
  color: #999;
}

.templateTable {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

/* 价格显示样式 */
.priceText {
  font-weight: 600;
}

.priceSuccess {
  color: #52c41a;
}

.priceWarning {
  color: #faad14;
}

/* 操作按钮样式 */
.actionButton {
  transition: all 0.3s;
}

.actionButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 标签样式 */
.typeTag {
  font-size: 12px;
  border-radius: 4px;
}

.countTag {
  background-color: #f0f0f0;
  color: #666;
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .typeSelectionRow {
    flex-direction: column;
    align-items: flex-start;
  }

  .typeSelect {
    width: 100%;
  }
}
