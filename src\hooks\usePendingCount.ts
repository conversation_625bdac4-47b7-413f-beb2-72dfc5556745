import { useState, useEffect } from "react";
import { getPendingManifestCount } from "../services/forecastService";

/**
 * 自定义Hook，获取待审核预报数量
 * @param refreshInterval 刷新间隔（毫秒）默认为60秒
 * @returns 待审核预报数量
 */
const usePendingCount = (refreshInterval = 60000) => {
  const [pendingCount, setPendingCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  const loadPendingCount = async () => {
    try {
      setLoading(true);
      const count = await getPendingManifestCount();
      setPendingCount(count);
    } catch (error) {
      console.error("获取待审核数量失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和定时刷新
  useEffect(() => {
    loadPendingCount();

    const timer = setInterval(() => {
      loadPendingCount();
    }, refreshInterval);

    return () => clearInterval(timer);
  }, [refreshInterval]);

  return { pendingCount, loading, refresh: loadPendingCount };
};

export default usePendingCount;
