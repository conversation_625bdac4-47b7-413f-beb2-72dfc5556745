import React, { useEffect, useState, useRef } from "react";
import {
  Drawer,
  Descriptions,
  Table,
  Spin,
  Alert,
  Empty,
  Timeline,
  Card,
  Button,
  Modal,
  Form,
  Input,
  DatePicker,
  InputNumber,
  message,
  Space,
  Switch, // Added for boolean fields in compensation form
  Image,
} from "antd";
import {
  DollarCircleOutlined,
  StopOutlined,
  PayCircleOutlined, // Added for Compensation button
  EyeOutlined, // Added for details button
  DeleteOutlined, // Added for Destruction button
  RollbackOutlined, // Added for Return button
  FileSearchOutlined, // Added for View All button
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import type { ColumnsType } from "antd/es/table";
import {
  getManifestDetailsByManifestId,
  ManifestDetailData,
  ManifestItem,
  getManifestTrackings,
  TrackingEvent,
  FinancialAdjustment as BaseFinancialAdjustment,
} from "../../services/problemTicketService";

import {
  voidFinancialAdjustment,
  addCompensationAdjustment,
  CompensationAdjustmentPayload,
  addReassignmentAdjustment,
  addReturnAdjustment,
  ReturnAdjustmentPayload,
  addDestructionAdjustment,
  DestructionAdjustmentPayload,
} from "../../services/financialService";

import ImageUpload from "../upload/ImageUpload";
import { BusinessType } from "../../services/uploadService";

// 定义元数据接口用于赔偿记录
interface CompensationMetadata {
  isFreightDeduction?: boolean;
  isValueCompensation?: boolean;
  freightDeductionPercentage?: number;
  valueCompensationPercentage?: number;
  cargoValue?: number;
  totalFreightDeductionAmount?: number;
  totalValueCompensationAmount?: number;
  proofOfValueImageUrls?: string[];
}

// 扩展 FinancialAdjustment 接口来包含 additionalDetails 字段
interface FinancialAdjustment extends BaseFinancialAdjustment {
  additionalDetails?: Record<string, unknown>; // 从 API 返回的额外详情信息
  voidReason?: string; // 作废原因
}

// 定义错误对象的通用接口，包含更完整的响应结构
interface ApiErrorResponseData {
  errorMessage?: string;
  errorCode?: number;
}

interface ApiError {
  message: string;
  response?: {
    status?: number;
    statusText?: string;
    data?: ApiErrorResponseData;
  };
}

interface ManifestDetailDrawerProps {
  manifestId: number | null;
  visible: boolean;
  onClose: () => void;
  scrollToAdjustments?: boolean; // 是否滚动到费用调整明细部分
}

const ManifestDetailDrawer: React.FC<ManifestDetailDrawerProps> = ({
  manifestId,
  visible,
  onClose,
  scrollToAdjustments = false,
}) => {
  // 创建费用调整明细部分的引用
  const adjustmentsRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [manifestDetail, setManifestDetail] =
    useState<ManifestDetailData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [trackingsLoading, setTrackingsLoading] = useState<boolean>(false);
  const [trackings, setTrackings] = useState<TrackingEvent[]>([]);
  const [trackingsError, setTrackingsError] = useState<string | null>(null);

  // 作废费用调整相关状态
  const [voidModalVisible, setVoidModalVisible] = useState<boolean>(false);
  const [voidForm] = Form.useForm();
  const [submittingVoid, setSubmittingVoid] = useState<boolean>(false);
  const [currentAdjustmentId, setCurrentAdjustmentId] = useState<number | null>(
    null
  );

  // 赔偿相关状态 (Compensation related state)
  const [compensationModalVisible, setCompensationModalVisible] =
    useState<boolean>(false);
  const [compensationForm] = Form.useForm();
  const [submittingCompensation, setSubmittingCompensation] =
    useState<boolean>(false);
  // State to manage conditional fields for compensation form
  const [isFreightDeductionEnabled, setIsFreightDeductionEnabled] =
    useState<boolean>(true);
  const [isValueCompensationEnabled, setIsValueCompensationEnabled] =
    useState<boolean>(true);
  const [cargoValue, setCargoValue] = useState<number>(0);
  const [freightDeductionPercentage, setFreightDeductionPercentage] =
    useState<number>(100);
  const [valueCompensationPercentage, setValueCompensationPercentage] =
    useState<number>(100);
  const [totalFreightDeductionAmount, setTotalFreightDeductionAmount] =
    useState<number>(0);

  // 改派相关状态 (Reassignment related state)
  const [reassignmentModalVisible, setReassignmentModalVisible] =
    useState<boolean>(false);
  const [reassignmentForm] = Form.useForm();
  const [submittingReassignment, setSubmittingReassignment] =
    useState<boolean>(false);

  // 销毁相关状态 (Destruction related state)
  const [destructionModalVisible, setDestructionModalVisible] =
    useState<boolean>(false);
  const [destructionForm] = Form.useForm();
  const [submittingDestruction, setSubmittingDestruction] =
    useState<boolean>(false);

  // 退回相关状态 (Return related state)
  const [returnModalVisible, setReturnModalVisible] = useState<boolean>(false);
  const [returnForm] = Form.useForm();
  const [submittingReturn, setSubmittingReturn] = useState<boolean>(false);

  // 赔偿详情相关状态 (Compensation Details related state)
  const [detailsModalVisible, setDetailsModalVisible] =
    useState<boolean>(false);
  const [selectedAdjustment, setSelectedAdjustment] =
    useState<FinancialAdjustment | null>(null);
  const [detailsForm] = Form.useForm();
  const [totalValueCompensationAmount, setTotalValueCompensationAmount] =
    useState<number>(0);
  const [totalCompensationAmount, setTotalCompensationAmount] =
    useState<number>(0);

  // 计算费用金额
  const calculateCompensationAmounts = () => {
    if (!manifestDetail) return;

    // 获取当前表单值
    const formValues = compensationForm.getFieldsValue();
    const currentFreightDeduction = formValues.isFreightDeduction || false;
    const currentValueCompensation = formValues.isValueCompensation || false;

    // 计算基础运费总额
    const baseCost =
      (manifestDetail.cost || 0) +
      (manifestDetail.overLengthSurcharge || 0) +
      (manifestDetail.remoteAreaSurcharge || 0);

    // 计算扣运费总额 = 基础运费 * 扣运费百分比
    const calculatedFreightDeduction = currentFreightDeduction
      ? (baseCost * (formValues.freightDeductionPercentage || 0)) / 100
      : 0;
    setTotalFreightDeductionAmount(calculatedFreightDeduction);

    // 计算保价赔偿总额 = 货值 * 保价赔偿百分比
    const calculatedValueCompensation = currentValueCompensation
      ? ((formValues.cargoValue || 0) *
          (formValues.valueCompensationPercentage || 0)) /
        100
      : 0;
    setTotalValueCompensationAmount(calculatedValueCompensation);

    // 计算总赔偿金额 = 扣运费总额 + 保价赔偿总额
    const calculatedTotalCompensation =
      calculatedFreightDeduction + calculatedValueCompensation;
    setTotalCompensationAmount(calculatedTotalCompensation);

    // 更新表单数据
    compensationForm.setFieldsValue({
      totalFreightDeductionAmount: calculatedFreightDeduction.toFixed(2),
      totalValueCompensationAmount: calculatedValueCompensation.toFixed(2),
      totalCompensationAmount: calculatedTotalCompensation.toFixed(2),
    });
  };

  // 监听表单字段变化以重新计算金额
  useEffect(() => {
    calculateCompensationAmounts();
  }, [
    isFreightDeductionEnabled,
    isValueCompensationEnabled,
    freightDeductionPercentage,
    valueCompensationPercentage,
    cargoValue,
  ]);

  // 打开赔偿模态框 (Open Compensation Modal)
  const showCompensationModal = () => {
    if (!manifestDetail) return;

    // 计算基础运费总额
    const baseCost =
      (manifestDetail.cost || 0) +
      (manifestDetail.overLengthSurcharge || 0) +
      (manifestDetail.remoteAreaSurcharge || 0);

    // 计算初始扣运费总额 (100% of base cost)
    const initialFreightDeduction = baseCost;

    // 设置表单初始值
    compensationForm.setFieldsValue({
      manifestId: manifestDetail.id,
      customerAccountId: manifestDetail.userId,
      currency: "CNY", // 固定为人民币
      isFreightDeduction: true,
      isValueCompensation: true,
      freightDeductionPercentage: 100,
      valueCompensationPercentage: 100,
      cargoValue: 0,
      totalFreightDeductionAmount: initialFreightDeduction.toFixed(2),
      totalValueCompensationAmount: "0.00",
      totalCompensationAmount: initialFreightDeduction.toFixed(2),
      effectiveDate: dayjs(), // 默认为今天
      proofOfValueImageUrls: [],
    });

    // 设置状态
    setIsFreightDeductionEnabled(true);
    setIsValueCompensationEnabled(true);
    setFreightDeductionPercentage(100);
    setValueCompensationPercentage(100);
    setCargoValue(0);
    setTotalFreightDeductionAmount(initialFreightDeduction);
    setTotalValueCompensationAmount(0);
    setTotalCompensationAmount(initialFreightDeduction);

    setCompensationModalVisible(true);
  };

  // 关闭赔偿模态框 (Close Compensation Modal)
  const handleCompensationModalCancel = () => {
    setCompensationModalVisible(false);
    compensationForm.resetFields();
  };

  // 打开改派模态框 (Open Reassignment Modal)
  const showReassignmentModal = () => {
    if (!manifestDetail) return;

    // 设置表单初始值
    reassignmentForm.setFieldsValue({
      manifestId: manifestDetail.id,
      customerAccountId: manifestDetail.userId,
      currency: "CNY", // 固定为人民币
      amount: 80, // 改派默认费用80元
      reassignmentNumber: "", // 空的改派单号
      effectiveDate: dayjs(), // 默认为今天
      description: "",
    });

    setReassignmentModalVisible(true);
  };

  // 关闭改派模态框 (Close Reassignment Modal)
  const handleReassignmentModalCancel = () => {
    setReassignmentModalVisible(false);
    reassignmentForm.resetFields();
  };

  // 提交改派表单 (Submit Reassignment Form)
  const handleReassignmentSubmit = async () => {
    try {
      // 验证表单
      const values = await reassignmentForm.validateFields();

      // 格式化日期
      const effectiveDate = dayjs(values.effectiveDate).format("YYYY-MM-DD");

      // 准备提交数据
      const payload = {
        ...values,
        effectiveDate,
      };

      setSubmittingReassignment(true);

      // 调用API添加改派调整
      const result = await addReassignmentAdjustment(payload);

      message.success("改派调整添加成功");
      setReassignmentModalVisible(false);
      reassignmentForm.resetFields();

      // 刷新运单详情显示新调整
      if (manifestId) {
        // 触发刷新
        getManifestDetailsByManifestId(manifestId)
          .then(setManifestDetail)
          .catch(setError);
      }
    } catch (errorInfo) {
      console.error("改派表单验证失败或提交失败:", errorInfo);
      const apiError = errorInfo as ApiError;
      let displayMessage = "改派信息提交失败，请检查表单并重试。";
      if (apiError.response?.data?.errorMessage) {
        displayMessage = apiError.response.data.errorMessage;
      } else if (apiError.message) {
        displayMessage = apiError.message;
      }
      message.error(displayMessage);
    } finally {
      setSubmittingReassignment(false);
    }
  };

  // 显示销毁模态框 (Open Destruction Modal)
  const showDestructionModal = () => {
    if (!manifestDetail) return;

    // 设置表单初始值
    destructionForm.setFieldsValue({
      manifestId: manifestDetail.id,
      customerAccountId: manifestDetail.userId,
      currency: "CNY", // 固定为人民币
      amount: 20, // 销毁默认费用20元
      effectiveDate: dayjs(), // 默认为今天
      description: "",
    });

    setDestructionModalVisible(true);
  };

  // 关闭销毁模态框 (Close Destruction Modal)
  const handleDestructionModalCancel = () => {
    setDestructionModalVisible(false);
    destructionForm.resetFields();
  };

  // 提交销毁表单 (Submit Destruction Form)
  const handleDestructionSubmit = async () => {
    try {
      // 验证表单
      const values = await destructionForm.validateFields();

      // 验证金额
      if (!values.amount || values.amount <= 0) {
        message.error("销毁费用必须大于0");
        return;
      }

      // 格式化日期
      const effectiveDate = dayjs(values.effectiveDate).format("YYYY-MM-DD");

      // 准备提交数据
      const payload: DestructionAdjustmentPayload = {
        manifestId: manifestDetail.id,
        customerAccountId: manifestDetail.userId,
        amount: values.amount,
        description: values.description,
        effectiveDate,
        currency: "CNY", // 固定为人民币
      };

      setSubmittingDestruction(true);

      // 调用API添加销毁调整
      await addDestructionAdjustment(payload);

      message.success("销毁调整添加成功");
      setDestructionModalVisible(false);
      destructionForm.resetFields();

      // 刷新运单详情显示新调整
      if (manifestId) {
        // 触发刷新
        getManifestDetailsByManifestId(manifestId)
          .then(setManifestDetail)
          .catch(setError);
      }
    } catch (errorInfo) {
      console.error("销毁表单验证失败或提交失败:", errorInfo);
      const apiError = errorInfo as ApiError;
      let displayMessage = "销毁信息提交失败，请检查表单并重试。";
      if (apiError.response?.data?.errorMessage) {
        displayMessage = apiError.response.data.errorMessage;
      } else if (apiError.message) {
        displayMessage = apiError.message;
      }
      message.error(displayMessage);
    } finally {
      setSubmittingDestruction(false);
    }
  };

  // 显示退回模态框 (Open Return Modal)
  const showReturnModal = () => {
    if (!manifestDetail) return;

    // 设置表单初始值
    returnForm.setFieldsValue({
      manifestId: manifestDetail.id,
      customerAccountId: manifestDetail.userId,
      currency: "CNY", // 固定为人民币
      amount: 80, // 退回默认费用80元
      effectiveDate: dayjs(), // 默认为今天
      description: "",
    });

    setReturnModalVisible(true);
  };

  // 关闭退回模态框 (Close Return Modal)
  const handleReturnModalCancel = () => {
    setReturnModalVisible(false);
    returnForm.resetFields();
  };

  // 提交退回表单 (Submit Return Form)
  const handleReturnSubmit = async () => {
    if (!manifestDetail) {
      message.error("无法获取运单详情，请重试");
      return;
    }

    try {
      // 验证表单
      const values = await returnForm.validateFields();

      // 验证金额
      if (!values.amount || values.amount <= 0) {
        message.error("退回金额必须大于0");
        return;
      }

      // 格式化日期
      const effectiveDate = dayjs(values.effectiveDate).format("YYYY-MM-DD");

      // 准备提交数据
      const payload: ReturnAdjustmentPayload = {
        manifestId: manifestDetail.id,
        customerAccountId: manifestDetail.userId,
        amount: values.amount,
        description: values.description,
        effectiveDate,
        currency: "CNY", // 固定为人民币
      };

      setSubmittingReturn(true);

      // 调用API添加退回调整
      await addReturnAdjustment(payload);

      message.success("退回调整添加成功");
      setReturnModalVisible(false);
      returnForm.resetFields();

      // 刷新运单详情显示新调整
      if (manifestId) {
        // 触发刷新
        getManifestDetailsByManifestId(manifestId)
          .then(setManifestDetail)
          .catch(setError);
      }
    } catch (errorInfo) {
      console.error("退回表单验证失败或提交失败:", errorInfo);
      const apiError = errorInfo as ApiError;
      let displayMessage = "退回信息提交失败，请检查表单并重试。";
      if (apiError.response?.data?.errorMessage) {
        displayMessage = apiError.response.data.errorMessage;
      } else if (apiError.message) {
        displayMessage = apiError.message;
      }
      message.error(displayMessage);
    } finally {
      setSubmittingReturn(false);
    }
  };

  // 监听表单字段变化并更新状态
  const handleFormValuesChange = (
    changedValues: Record<string, unknown>,
    allValues: Record<string, unknown>
  ) => {
    // 如果货值变化，更新cargoValue状态
    if ("cargoValue" in changedValues) {
      const newCargoValue = Number(changedValues.cargoValue) || 0;
      setCargoValue(newCargoValue);
      // 如果启用了保价赔偿，重新计算金额
      if (allValues.isValueCompensation) {
        calculateCompensationAmounts();
      }
    }

    // 如果扣运费百分比变化，更新状态
    if ("freightDeductionPercentage" in changedValues) {
      const newPercentage =
        Number(changedValues.freightDeductionPercentage) || 0;
      setFreightDeductionPercentage(newPercentage);
      // 如果启用了扣运费，重新计算金额
      if (allValues.isFreightDeduction) {
        calculateCompensationAmounts();
      }
    }

    // 如果保价赔偿百分比变化，更新状态
    if ("valueCompensationPercentage" in changedValues) {
      const newPercentage =
        Number(changedValues.valueCompensationPercentage) || 0;
      setValueCompensationPercentage(newPercentage);
      // 如果启用了保价赔偿，重新计算金额
      if (allValues.isValueCompensation) {
        calculateCompensationAmounts();
      }
    }
  };

  // 提交赔偿表单 (Submit Compensation Form)
  const handleCompensationSubmit = async () => {
    if (!manifestDetail) return;
    try {
      const values = await compensationForm.validateFields();

      // 验证表单数据
      // 1. 如果要赔偿货值，那么货值要大于0
      if (values.isValueCompensation && (!cargoValue || cargoValue <= 0)) {
        message.error("如果需要赔偿货值，请输入大于0的货值");
        return;
      }

      // 2. 如果要减免运费，那么减免运费总额要大于0
      if (
        values.isFreightDeduction &&
        (!totalFreightDeductionAmount || totalFreightDeductionAmount <= 0)
      ) {
        message.error("如果需要减免运费，请确保减免运费总额大于0");
        return;
      }

      // 3. 总赔偿金额要大于0
      if (!totalCompensationAmount || totalCompensationAmount <= 0) {
        message.error("总赔偿金额必须大于0");
        return;
      }

      setSubmittingCompensation(true);

      // 直接使用表单中的货值而不是状态变量
      const formCargoValue = Number(values.cargoValue) || 0;

      const payload: CompensationAdjustmentPayload = {
        manifestId: manifestDetail.id,
        customerAccountId: manifestDetail.userId,
        isFreightDeduction: values.isFreightDeduction,
        freightDeductionPercentage: values.isFreightDeduction
          ? freightDeductionPercentage
          : undefined,
        totalFreightDeductionAmount: values.isFreightDeduction
          ? -Math.abs(totalFreightDeductionAmount)
          : undefined,
        isValueCompensation: values.isValueCompensation,
        cargoValue: values.isValueCompensation ? formCargoValue : undefined, // 使用表单中的值
        valueCompensationPercentage: values.isValueCompensation
          ? valueCompensationPercentage
          : undefined,
        totalValueCompensationAmount: values.isValueCompensation
          ? -Math.abs(totalValueCompensationAmount)
          : undefined,
        totalCompensationAmount: -Math.abs(totalCompensationAmount), // Ensure amount is negative
        description: values.description,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        currency: "CNY", // 固定为人民币
        proofOfValueImageUrls: Array.isArray(values.proofOfValueImageUrls)
          ? values.proofOfValueImageUrls
          : [],
      };

      console.log("Submitting compensation with cargo value:", formCargoValue);

      await addCompensationAdjustment(payload);
      message.success("赔偿信息提交成功");
      setCompensationModalVisible(false);
      compensationForm.resetFields();
      // Refresh manifest details to show new adjustment
      if (manifestId) {
        // Trigger refresh (assuming getManifestDetailsByManifestId updates the view)
        getManifestDetailsByManifestId(manifestId)
          .then(setManifestDetail)
          .catch(setError);
      }
    } catch (errorInfo) {
      console.error("赔偿表单验证失败或提交失败:", errorInfo);
      const apiError = errorInfo as ApiError;
      let displayMessage = "赔偿信息提交失败，请检查表单并重试。";
      if (apiError.response?.data?.errorMessage) {
        displayMessage = apiError.response.data.errorMessage;
      } else if (apiError.message) {
        displayMessage = apiError.message;
      }
      message.error(displayMessage);
    } finally {
      setSubmittingCompensation(false);
    }
  };

  useEffect(() => {
    if (visible && manifestId !== null) {
      setLoading(true);
      setError(null);
      setManifestDetail(null);
      getManifestDetailsByManifestId(manifestId)
        .then((data: ManifestDetailData) => {
          console.log("获取到的运单详情数据:", data);

          // 处理可能的字段名不一致问题
          if (
            data.financialAdjustments &&
            data.financialAdjustments.length > 0
          ) {
            console.log(
              "费用调整数据示例(处理前):",
              data.financialAdjustments[0]
            );

            // 转换字段名称
            data.financialAdjustments = data.financialAdjustments.map(
              (item) => {
                // 如果存在 is_void 字段但不存在 isVoid 字段，则进行转换
                if (item.is_void !== undefined && item.isVoid === undefined) {
                  return {
                    ...item,
                    isVoid: item.is_void,
                  };
                }
                return item;
              }
            );

            console.log(
              "费用调整数据示例(处理后):",
              data.financialAdjustments[0]
            );
          }

          setManifestDetail(data);
        })
        .catch((err: unknown) => {
          console.error("获取运单详情失败 - 原始错误对象:", err);

          const apiError = err as ApiError;
          let displayMessage = "获取运单详情失败，请稍后重试。";
          const statusCode = apiError.response?.status;
          const backendErrorMessage = apiError.response?.data?.errorMessage;
          const genericMessage = apiError.message;

          if (statusCode && statusCode >= 500 && statusCode <= 599) {
            displayMessage =
              backendErrorMessage ||
              `服务器内部错误 (状态码: ${statusCode})，请稍后重试。`;
          } else if (backendErrorMessage) {
            displayMessage = backendErrorMessage;
          } else if (genericMessage) {
            displayMessage = genericMessage;
          }

          setError(displayMessage);
        })
        .finally(() => {
          setLoading(false);
        });

      setTrackingsLoading(true);
      setTrackingsError(null);
      setTrackings([]);
      getManifestTrackings(manifestId)
        .then((trackingData: TrackingEvent[]) => {
          setTrackings(trackingData);
        })
        .catch((err: unknown) => {
          console.error("获取物流轨迹失败 - 原始错误对象:", err);
          const apiError = err as ApiError;
          let displayMessage = "获取物流轨迹失败，请稍后重试。";
          const statusCode = apiError.response?.status;
          const backendErrorMessage = apiError.response?.data?.errorMessage;
          const genericMessage = apiError.message;
          if (statusCode && statusCode >= 500 && statusCode <= 599) {
            displayMessage =
              backendErrorMessage ||
              `获取物流轨迹时发生服务器错误 (状态码: ${statusCode})。`;
          } else if (backendErrorMessage) {
            displayMessage = backendErrorMessage;
          } else if (genericMessage) {
            displayMessage = genericMessage;
          }
          setTrackingsError(displayMessage);
        })
        .finally(() => {
          setTrackingsLoading(false);
        });
    } else if (!visible) {
      setManifestDetail(null);
      setError(null);
      setLoading(false);
      setTrackings([]);
      setTrackingsError(null);
      setTrackingsLoading(false);
    }
  }, [visible, manifestId]);

  // 处理滚动到费用调整明细部分
  useEffect(() => {
    // 当数据加载完成且需要滚动到费用调整明细部分时
    if (
      !loading &&
      manifestDetail &&
      scrollToAdjustments &&
      adjustmentsRef.current
    ) {
      // 使用 setTimeout 确保 DOM 已经渲染完成
      setTimeout(() => {
        adjustmentsRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 300);
    }
  }, [loading, manifestDetail, scrollToAdjustments]);

  // 打开调整详情模态框 (Open Adjustment Details Modal)
  const showDetailsModal = (adjustment: FinancialAdjustment) => {
    setSelectedAdjustment(adjustment);

    if (adjustment.adjustmentType === "COMPENSATION") {
      // 解析 additionalDetails 字段来获取赔偿详情
      const metadata: CompensationMetadata = {};

      try {
        // 检查是否有 additionalDetails 字段
        if (adjustment.additionalDetails) {
          // 如果是字符串，尝试解析为对象
          let details = adjustment.additionalDetails;
          if (typeof details === "string") {
            try {
              details = JSON.parse(details);
            } catch (e) {
              console.warn("Failed to parse additionalDetails string:", e);
            }
          }

          // 现在从 details 对象提取元数据
          if (details && typeof details === "object") {
            // 常规的下划线形式字段名
            metadata.isFreightDeduction =
              details.is_freight_deduction !== undefined
                ? details.is_freight_deduction
                : false;
            metadata.isValueCompensation =
              details.is_value_compensation !== undefined
                ? details.is_value_compensation
                : false;
            metadata.freightDeductionPercentage =
              details.freight_deduction_percentage !== undefined
                ? details.freight_deduction_percentage
                : 0;
            metadata.valueCompensationPercentage =
              details.value_compensation_percentage !== undefined
                ? details.value_compensation_percentage
                : 0;
            metadata.totalFreightDeductionAmount =
              details.total_freight_deduction_amount !== undefined
                ? Math.abs(details.total_freight_deduction_amount)
                : 0;
            metadata.totalValueCompensationAmount =
              details.total_value_compensation_amount !== undefined
                ? Math.abs(details.total_value_compensation_amount)
                : 0;

            // 验证是否有驼峰式命名的字段（兼容性）
            if (
              metadata.isFreightDeduction === false &&
              details.isFreightDeduction !== undefined
            ) {
              metadata.isFreightDeduction = details.isFreightDeduction;
            }
            if (
              metadata.isValueCompensation === false &&
              details.isValueCompensation !== undefined
            ) {
              metadata.isValueCompensation = details.isValueCompensation;
            }
            if (
              metadata.freightDeductionPercentage === 0 &&
              details.freightDeductionPercentage !== undefined
            ) {
              metadata.freightDeductionPercentage =
                details.freightDeductionPercentage;
            }
            if (
              metadata.valueCompensationPercentage === 0 &&
              details.valueCompensationPercentage !== undefined
            ) {
              metadata.valueCompensationPercentage =
                details.valueCompensationPercentage;
            }
            if (
              metadata.totalFreightDeductionAmount === 0 &&
              details.totalFreightDeductionAmount !== undefined
            ) {
              metadata.totalFreightDeductionAmount = Math.abs(
                details.totalFreightDeductionAmount
              );
            }
            if (
              metadata.totalValueCompensationAmount === 0 &&
              details.totalValueCompensationAmount !== undefined
            ) {
              metadata.totalValueCompensationAmount = Math.abs(
                details.totalValueCompensationAmount
              );
            }

            // 货值和图片链接可能没有直接保存
            metadata.cargoValue =
              details.cargo_value || details.cargoValue || 0;
            metadata.proofOfValueImageUrls =
              details.proof_of_value_image_urls ||
              details.proofOfValueImageUrls ||
              [];
          }
        }
      } catch (error) {
        console.error(
          "Error processing compensation additionalDetails:",
          error
        );
      }

      // 设置只读表单的默认值
      detailsForm.setFieldsValue({
        manifestId: adjustment.id, // 使用调整记录ID
        customerAccountId: adjustment.customerAccountId,
        currency: adjustment.currency,
        isFreightDeduction: metadata.isFreightDeduction,
        isValueCompensation: metadata.isValueCompensation,
        freightDeductionPercentage: metadata.freightDeductionPercentage,
        valueCompensationPercentage: metadata.valueCompensationPercentage,
        cargoValue: metadata.cargoValue,
        totalFreightDeductionAmount: metadata.totalFreightDeductionAmount,
        totalValueCompensationAmount: metadata.totalValueCompensationAmount,
        totalCompensationAmount: Math.abs(adjustment.amount),
        description: adjustment.description,
        effectiveDate: dayjs(adjustment.effectiveDate),
        proofOfValueImageUrls: metadata.proofOfValueImageUrls,
        // 作废相关信息
        isVoid: adjustment.isVoid,
        voidReason: adjustment.voidReason,
      });
    } else if (adjustment.adjustmentType === "REASSIGNMENT") {
      // 改派调整详情
      let reassignmentNumber = "";

      try {
        // 尝试从 additionalDetails 获取改派单号
        if (adjustment.additionalDetails) {
          let details = adjustment.additionalDetails;
          if (typeof details === "string") {
            try {
              details = JSON.parse(details);
            } catch (e) {
              console.warn("Failed to parse additionalDetails string:", e);
            }
          }

          if (details && typeof details === "object") {
            // 从详情中提取改派单号，先查找下划线形式再查找驼峰式
            reassignmentNumber =
              details.reassignment_number || details.reassignmentNumber || "";
          }
        }
      } catch (error) {
        console.error(
          "Error processing reassignment additionalDetails:",
          error
        );
      }

      // 设置表单值
      detailsForm.setFieldsValue({
        manifestId: adjustment.id,
        customerAccountId: adjustment.customerAccountId,
        currency: adjustment.currency,
        amount: Math.abs(adjustment.amount),
        reassignmentNumber: reassignmentNumber,
        description: adjustment.description,
        effectiveDate: dayjs(adjustment.effectiveDate),
        // 作废相关信息
        isVoid: adjustment.isVoid,
        voidReason: adjustment.voidReason,
      });
    } else if (adjustment.adjustmentType === "DESTRUCTION") {
      // 销毁调整详情
      // 设置表单值
      detailsForm.setFieldsValue({
        manifestId: adjustment.id,
        customerAccountId: adjustment.customerAccountId,
        amount: Math.abs(adjustment.amount),
        currency: adjustment.currency,
        description: adjustment.description,
        effectiveDate: dayjs(adjustment.effectiveDate),
        // 作废相关信息
        isVoid: adjustment.isVoid,
        voidReason: adjustment.voidReason,
      });
    } else if (adjustment.adjustmentType === "RETURN") {
      // 退回调整详情
      // 设置表单值
      detailsForm.setFieldsValue({
        manifestId: adjustment.id,
        customerAccountId: adjustment.customerAccountId,
        amount: Math.abs(adjustment.amount),
        currency: adjustment.currency,
        description: adjustment.description,
        effectiveDate: dayjs(adjustment.effectiveDate),
        // 作废相关信息
        isVoid: adjustment.isVoid,
        voidReason: adjustment.voidReason,
      });
    }

    setDetailsModalVisible(true);
  };

  // 关闭赔偿详情模态框 (Close Compensation Details Modal)
  const handleDetailsModalClose = () => {
    setDetailsModalVisible(false);
    setSelectedAdjustment(null);
    detailsForm.resetFields();
  };

  // 跳转到费用调整页面
  const handleGoToAdjustmentsPage = () => {
    navigate("/billing-finance/adjustments");
  };

  // 打开作废费用调整模态框
  const showVoidModal = (adjustmentId: number) => {
    setCurrentAdjustmentId(adjustmentId);
    voidForm.resetFields();
    setVoidModalVisible(true);
  };

  // 关闭作废费用调整模态框
  const handleVoidModalCancel = () => {
    setVoidModalVisible(false);
    setCurrentAdjustmentId(null);
    voidForm.resetFields();
  };

  // 提交作废费用调整
  const handleVoidSubmit = async () => {
    if (currentAdjustmentId === null) {
      message.error("未选择要作废的费用调整记录");
      return;
    }

    try {
      const values = await voidForm.validateFields();

      setSubmittingVoid(true);

      // 调用API作废费用调整
      await voidFinancialAdjustment({
        id: currentAdjustmentId,
        voidReason: values.reason, // 使用表单中的 reason 字段
      });

      message.success("费用调整作废成功");
      setVoidModalVisible(false);
      voidForm.resetFields();
      setCurrentAdjustmentId(null);

      // 重新加载运单详情以更新费用调整状态
      if (manifestId) {
        getManifestDetailsByManifestId(manifestId)
          .then((data) => {
            setManifestDetail(data);
          })
          .catch((err) => {
            console.error("重新加载运单详情失败:", err);
          });
      }
    } catch (error) {
      console.error("作废费用调整失败:", error);
      message.error("作废费用调整失败，请稍后重试");
    } finally {
      setSubmittingVoid(false);
    }
  };

  const itemColumns: ColumnsType<ManifestItem> = [
    { title: "物品名称", dataIndex: "name", key: "name" },
    { title: "数量", dataIndex: "quantity", key: "quantity", align: "right" },
    {
      title: "单价",
      dataIndex: "price",
      key: "price",
      align: "right",
      render: (val: number) => `¥${val.toFixed(2)}`,
    },
    {
      title: "总价值",
      dataIndex: "value",
      key: "value",
      align: "right",
      render: (val: number) => `¥${val.toFixed(2)}`,
    },
    { title: "重量 (kg)", dataIndex: "weight", key: "weight", align: "right" },
  ];

  // 费用调整明细表格列定义
  const financialAdjustmentColumns: ColumnsType<FinancialAdjustment> = [
    {
      title: "调整类型",
      dataIndex: "adjustmentType",
      key: "adjustmentType",
      render: (type: string) => {
        if (type === "COMPENSATION") return "赔偿";
        if (type === "REASSIGNMENT") return "改派";
        if (type === "DESTRUCTION") return "销毁";
        if (type === "RETURN") return "退回";
        return type;
      },
    },
    {
      title: "调整金额",
      dataIndex: "amount",
      key: "amount",
      align: "right",
      render: (val: number, record: FinancialAdjustment) => {
        const color = val < 0 ? "#ff4d4f" : "#52c41a";
        return (
          <span style={{ color }}>{`${record.currency} ${val.toFixed(
            2
          )}`}</span>
        );
      },
    },
    {
      title: "生效日期",
      dataIndex: "effectiveDate",
      key: "effectiveDate",
      render: (date: string) => dayjs(date).format("YYYY-MM-DD"),
    },
    { title: "创建者", dataIndex: "creatorNickname", key: "creatorNickname" },
    {
      title: "状态",
      dataIndex: "isVoid",
      key: "status",
      align: "center",
      render: (isVoid: boolean, record: FinancialAdjustment) => {
        console.log(`记录ID ${record.id} 的 isVoid 值:`, isVoid, record);
        return isVoid ? (
          <span style={{ color: "#ff4d4f" }}>已作废</span>
        ) : (
          <span style={{ color: "#52c41a" }}>生效中</span>
        );
      },
    },
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (_, record: FinancialAdjustment) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => showDetailsModal(record)}
          >
            详情
          </Button>

          {!record.isVoid && (
            <Button
              type="text"
              danger
              icon={<StopOutlined />}
              size="small"
              onClick={() => showVoidModal(record.id)}
            >
              作废
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Drawer
        title={`运单详情 (ID: ${manifestId || "N/A"})`}
        placement="right"
        width={720}
        onClose={onClose}
        open={visible}
        destroyOnClose
      >
        {loading && (
          <div style={{ textAlign: "center", padding: "20px" }}>
            <Spin size="large" tip="加载详情中..." />
          </div>
        )}
        {error && (
          <Alert
            message="加载运单详情错误"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
            style={{ marginBottom: 16 }}
          />
        )}
        {!loading && !error && !manifestDetail && visible && (
          <Empty
            description="暂无运单详情数据或未选择运单"
            style={{ marginTop: "20px" }}
          />
        )}
        {manifestDetail && (
          <>
            <Descriptions title="基础信息" bordered column={2} size="small">
              <Descriptions.Item label="运单ID">
                {manifestDetail.id}
              </Descriptions.Item>
              <Descriptions.Item label="快递单号">
                {manifestDetail.expressNumber}
              </Descriptions.Item>
              <Descriptions.Item label="订单号">
                {manifestDetail.orderNumber}
              </Descriptions.Item>
              <Descriptions.Item label="创建用户">
                {manifestDetail.userNickname} (ID: {manifestDetail.userId})
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {manifestDetail.createTime}
              </Descriptions.Item>
              <Descriptions.Item label="预报批次ID">
                {manifestDetail.preRegistrationBatchId}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions
              title="收件人信息"
              bordered
              column={2}
              size="small"
              style={{ marginTop: 24 }}
            >
              <Descriptions.Item label="收件人姓名">
                {manifestDetail.receiverName}
              </Descriptions.Item>
              <Descriptions.Item label="电话号码">
                {manifestDetail.receiverPhone}
              </Descriptions.Item>
              <Descriptions.Item label="邮编">
                {manifestDetail.receiverZipCode}
              </Descriptions.Item>
              <Descriptions.Item label="收件地址" span={2}>
                {manifestDetail.receiverAddress}
              </Descriptions.Item>
            </Descriptions>

            <h3 style={{ marginTop: 24, marginBottom: 16 }}>物品列表</h3>
            <Table
              columns={itemColumns}
              dataSource={manifestDetail.items}
              rowKey="name"
              pagination={false}
              bordered
              size="small"
              style={{ marginBottom: 24 }}
            />

            {/* 包裹信息 */}
            {(manifestDetail.length !== undefined ||
              manifestDetail.width !== undefined ||
              manifestDetail.height !== undefined ||
              manifestDetail.weight !== undefined ||
              manifestDetail.dimensionalWeight !== undefined) && (
              <Descriptions
                title="包裹信息"
                bordered
                column={2}
                size="small"
                style={{ marginTop: 24 }}
              >
                {manifestDetail.length !== undefined && (
                  <Descriptions.Item label="长度(cm)">
                    {manifestDetail.length}
                  </Descriptions.Item>
                )}
                {manifestDetail.width !== undefined && (
                  <Descriptions.Item label="宽度(cm)">
                    {manifestDetail.width}
                  </Descriptions.Item>
                )}
                {manifestDetail.height !== undefined && (
                  <Descriptions.Item label="高度(cm)">
                    {manifestDetail.height}
                  </Descriptions.Item>
                )}
                {manifestDetail.weight !== undefined && (
                  <Descriptions.Item label="实际重量(kg)">
                    {manifestDetail.weight}
                  </Descriptions.Item>
                )}
                {manifestDetail.dimensionalWeight !== undefined && (
                  <Descriptions.Item label="体积重量(kg)">
                    {manifestDetail.dimensionalWeight}
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}

            {/* 费用信息 */}
            {(manifestDetail.cost !== undefined ||
              manifestDetail.overLengthSurcharge !== undefined ||
              manifestDetail.remoteAreaSurcharge !== undefined) && (
              <Descriptions
                title="费用信息"
                bordered
                column={1}
                size="small"
                style={{ marginTop: 24 }}
              >
                {manifestDetail.cost !== undefined && (
                  <Descriptions.Item label="基本费用">
                    ¥{manifestDetail.cost.toFixed(2)}
                  </Descriptions.Item>
                )}
                {manifestDetail.overLengthSurcharge !== undefined && (
                  <Descriptions.Item label="超长费">
                    ¥{manifestDetail.overLengthSurcharge.toFixed(2)}
                  </Descriptions.Item>
                )}
                {manifestDetail.remoteAreaSurcharge !== undefined && (
                  <Descriptions.Item label="偏远费">
                    ¥{manifestDetail.remoteAreaSurcharge.toFixed(2)}
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}

            {/* 费用调整明细 */}
            <div
              ref={adjustmentsRef} // 添加 ref 引用
              style={{
                marginTop: 24,
                marginBottom: 16,
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>费用调整明细</h3>
                <div style={{ display: "flex", gap: "8px" }}>
                  <Button
                    type="primary"
                    icon={<PayCircleOutlined />}
                    onClick={showReassignmentModal}
                    size="small"
                    style={{
                      backgroundColor: "#1890ff",
                      borderColor: "#1890ff",
                    }}
                  >
                    改派
                  </Button>
                  <Button
                    type="primary"
                    icon={<PayCircleOutlined />}
                    onClick={showCompensationModal}
                    size="small"
                    style={{
                      backgroundColor: "#52c41a",
                      borderColor: "#52c41a",
                    }}
                  >
                    赔偿
                  </Button>
                  <Button
                    type="primary"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={showDestructionModal}
                    size="small"
                  >
                    销毁
                  </Button>
                  <Button
                    type="primary"
                    icon={<RollbackOutlined />}
                    onClick={showReturnModal}
                    size="small"
                    style={{
                      backgroundColor: "#faad14",
                      borderColor: "#faad14",
                    }}
                  >
                    退回
                  </Button>
                  <Button
                    type="default"
                    icon={<FileSearchOutlined />}
                    onClick={handleGoToAdjustmentsPage}
                    size="small"
                    style={{ marginLeft: "8px" }}
                  >
                    查看全部
                  </Button>
                </div>
              </div>
            </div>

            {manifestDetail.financialAdjustments &&
            manifestDetail.financialAdjustments.length > 0 ? (
              <Table
                columns={financialAdjustmentColumns}
                dataSource={manifestDetail.financialAdjustments}
                rowKey="id"
                pagination={false}
                bordered
                size="small"
                style={{ marginBottom: 24 }}
              />
            ) : (
              <Empty
                description="暂无费用调整记录"
                style={{ margin: "20px 0" }}
              />
            )}

            {/* 总费用显示 */}
            {manifestDetail.totalFee !== undefined && (
              <Card
                style={{
                  marginTop: 24,
                  marginBottom: 24,
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.09)",
                  borderRadius: "8px",
                  borderLeft: "4px solid #1890ff",
                }}
                bodyStyle={{
                  padding: "16px 24px",
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <DollarCircleOutlined
                    style={{
                      fontSize: "24px",
                      color: "#1890ff",
                      marginRight: "12px",
                    }}
                  />
                  <div>
                    <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                      总费用
                    </span>
                    <span
                      style={{
                        fontSize: "12px",
                        color: "#888",
                        marginLeft: "8px",
                      }}
                    >
                      (仅包含生效中的费用调整)
                    </span>
                  </div>
                </div>
                <div
                  style={{
                    fontSize: "20px",
                    fontWeight: "bold",
                    color: "#1890ff",
                  }}
                >
                  ¥{manifestDetail.totalFee.toFixed(2)}
                </div>
              </Card>
            )}

            <h3 style={{ marginTop: 24, marginBottom: 16 }}>物流轨迹</h3>
            {trackingsLoading && (
              <div style={{ textAlign: "center", padding: "20px" }}>
                <Spin tip="加载物流轨迹中..." />
              </div>
            )}
            {trackingsError && (
              <Alert
                message="加载物流轨迹错误"
                description={trackingsError}
                type="error"
                showIcon
                closable
                onClose={() => setTrackingsError(null)}
                style={{ marginBottom: 16 }}
              />
            )}
            {!trackingsLoading && !trackingsError && trackings.length === 0 && (
              <Empty description="暂无物流轨迹信息" />
            )}
            {!trackingsLoading && !trackingsError && trackings.length > 0 && (
              <Timeline>
                {trackings.map((event) => (
                  <Timeline.Item
                    key={event.id}
                    color={
                      event.status === -1
                        ? "red"
                        : event.status >= 8
                        ? "green"
                        : "blue"
                    }
                  >
                    <p>
                      <strong>{event.track}</strong>
                    </p>
                    <p style={{ fontSize: "0.85em", color: "#888" }}>
                      时间: {event.time || event.createTime}
                      {event.place && ` | 地点: ${event.place}`}
                    </p>
                  </Timeline.Item>
                ))}
              </Timeline>
            )}
          </>
        )}
      </Drawer>

      {/* 作废费用调整模态框 */}
      <Modal
        title="作废费用调整"
        open={voidModalVisible}
        onCancel={handleVoidModalCancel}
        onOk={handleVoidSubmit}
        confirmLoading={submittingVoid}
        maskClosable={false}
        destroyOnClose
      >
        <Form form={voidForm} layout="vertical">
          <Form.Item
            name="reason"
            label="作废原因"
            rules={[{ required: true, message: "请输入作废原因" }]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 赔偿模态框 (Compensation Modal) */}
      <Modal
        title="添加赔偿"
        open={compensationModalVisible}
        onCancel={handleCompensationModalCancel}
        onOk={handleCompensationSubmit}
        confirmLoading={submittingCompensation}
        maskClosable={false}
        destroyOnClose
        width={900} // Wider modal for horizontal layout
      >
        <Form
          form={compensationForm}
          layout="vertical"
          name="compensationForm"
          onValuesChange={handleFormValuesChange}
          style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}
        >
          <Form.Item name="manifestId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="customerAccountId" hidden>
            <Input />
          </Form.Item>

          {/* Left side: Options and adjustments */}
          <div style={{ flex: "1", minWidth: "420px" }}>
            <div style={{ display: "flex", gap: "24px", marginBottom: "16px" }}>
              <Form.Item
                name="isFreightDeduction"
                label="是否减免运费"
                valuePropName="checked"
                style={{ flex: "1", margin: 0 }}
              >
                <Switch
                  checked={isFreightDeductionEnabled}
                  onChange={(checked) => {
                    setIsFreightDeductionEnabled(checked);
                    compensationForm.setFieldsValue({
                      isFreightDeduction: checked,
                    });
                    calculateCompensationAmounts();
                  }}
                  checkedChildren="是"
                  unCheckedChildren="否"
                />
              </Form.Item>

              <Form.Item
                name="isValueCompensation"
                label="是否赔偿货值"
                valuePropName="checked"
                style={{ flex: "1", margin: 0 }}
              >
                <Switch
                  checked={isValueCompensationEnabled}
                  onChange={(checked) => {
                    setIsValueCompensationEnabled(checked);
                    compensationForm.setFieldsValue({
                      isValueCompensation: checked,
                    });
                    calculateCompensationAmounts();
                  }}
                  checkedChildren="是"
                  unCheckedChildren="否"
                />
              </Form.Item>

              <Form.Item
                name="effectiveDate"
                label="生效日期"
                rules={[{ required: true, message: "请选择生效日期" }]}
                style={{ flex: "1", margin: 0 }}
              >
                <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
              </Form.Item>

              <Form.Item
                name="currency"
                label="货币"
                initialValue="CNY"
                style={{ flex: "1", margin: 0 }}
              >
                <Input
                  style={{ width: "100%" }}
                  disabled={true}
                  defaultValue="人民币 (CNY)"
                />
              </Form.Item>
            </div>

            {isFreightDeductionEnabled && (
              <div
                style={{ display: "flex", gap: "24px", marginBottom: "16px" }}
              >
                <Form.Item
                  name="freightDeductionPercentage"
                  label="减免运费比例 (%)"
                  rules={[
                    {
                      required: isFreightDeductionEnabled,
                      message: "请输入减免运费比例",
                    },
                    {
                      type: "number",
                      min: 0,
                      max: 100,
                      message: "请输入0-100之间的数字",
                    },
                  ]}
                  initialValue={100}
                  style={{ flex: "1", margin: 0 }}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="例如: 10 表示10%"
                    min={0}
                    max={100}
                    precision={0}
                    disabled={!isFreightDeductionEnabled}
                    onChange={(value) => {
                      if (value !== null) {
                        setFreightDeductionPercentage(Number(value));
                        calculateCompensationAmounts();
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name="totalFreightDeductionAmount"
                  label="减免运费总额"
                  style={{ flex: "1", margin: 0 }}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    disabled={true}
                    prefix="¥"
                  />
                </Form.Item>
              </div>
            )}

            {isValueCompensationEnabled && (
              <>
                <div
                  style={{ display: "flex", gap: "24px", marginBottom: "16px" }}
                >
                  <Form.Item
                    name="cargoValue"
                    label="货值"
                    rules={[
                      {
                        required: isValueCompensationEnabled,
                        message: "请输入货值",
                      },
                      {
                        type: "number",
                        min: 0.01,
                        message: "货值必须大于0",
                      },
                    ]}
                    initialValue={0}
                    style={{ flex: "1", margin: 0 }}
                  >
                    <InputNumber
                      style={{ width: "100%" }}
                      placeholder="请输入货值"
                      min={0}
                      precision={2}
                      prefix="¥"
                      disabled={!isValueCompensationEnabled}
                      onChange={(value) => {
                        if (value !== null) {
                          setCargoValue(Number(value));
                          calculateCompensationAmounts();
                        }
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="valueCompensationPercentage"
                    label="货值赔偿比例 (%)"
                    rules={[
                      {
                        required: isValueCompensationEnabled,
                        message: "请输入货值赔偿比例",
                      },
                      {
                        type: "number",
                        min: 0,
                        max: 100,
                        message: "请输入0-100之间的数字",
                      },
                    ]}
                    initialValue={100}
                    style={{ flex: "1", margin: 0 }}
                  >
                    <InputNumber
                      style={{ width: "100%" }}
                      min={0}
                      max={100}
                      precision={0}
                      disabled={!isValueCompensationEnabled}
                      onChange={(value) => {
                        if (value !== null) {
                          setValueCompensationPercentage(Number(value));
                          calculateCompensationAmounts();
                        }
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="totalValueCompensationAmount"
                    label="货值赔偿总额"
                    style={{ flex: "1", margin: 0 }}
                  >
                    <InputNumber
                      style={{ width: "100%" }}
                      disabled={true}
                      prefix="¥"
                    />
                  </Form.Item>
                </div>
              </>
            )}

            <div style={{ display: "flex", gap: "24px", marginBottom: "16px" }}>
              <Form.Item
                name="totalCompensationAmount"
                label="总赔偿金额"
                rules={[
                  {
                    required: true,
                    message: "请输入总赔偿金额",
                  },
                  {
                    validator: (_, value) => {
                      if (value > 0) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error("总赔偿金额必须大于0"));
                    },
                  },
                ]}
                style={{ flex: "1", margin: 0 }}
              >
                <InputNumber
                  style={{
                    width: "100%",
                    fontSize: "18px",
                    fontWeight: "bold",
                  }}
                  disabled={true}
                  prefix="¥"
                  className="highlighted-total"
                />
              </Form.Item>
            </div>
          </div>

          {/* Right side: Description and proof */}
          <div style={{ flex: "1", minWidth: "420px" }}>
            <Form.Item
              name="description"
              label="赔偿说明/备注"
              style={{ marginTop: 0 }}
            >
              <Input.TextArea
                rows={3}
                placeholder="请简要说明赔偿原因和计算方式"
              />
            </Form.Item>

            <Form.Item name="proofOfValueImageUrls" label="货值证明图片">
              <ImageUpload
                businessType={BusinessType.COMPENSATION_PROOF}
                maxCount={5}
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 调整详情模态框 (Adjustment Details Modal) */}
      <Modal
        title={
          selectedAdjustment?.adjustmentType === "REASSIGNMENT"
            ? "改派详情"
            : selectedAdjustment?.adjustmentType === "DESTRUCTION"
            ? "销毁详情"
            : selectedAdjustment?.adjustmentType === "RETURN"
            ? "退回详情"
            : "赔偿详情"
        }
        open={detailsModalVisible}
        onCancel={handleDetailsModalClose}
        footer={[
          <Button key="close" onClick={handleDetailsModalClose}>
            关闭
          </Button>,
        ]}
        maskClosable={false}
        destroyOnClose
        width={900}
      >
        {selectedAdjustment &&
          selectedAdjustment.adjustmentType === "REASSIGNMENT" && (
            <div>
              <Descriptions
                column={1}
                bordered
                size="small"
                style={{ marginBottom: "16px" }}
              >
                <Descriptions.Item label="改派单号">
                  {detailsForm.getFieldValue("reassignmentNumber") || "-"}
                </Descriptions.Item>

                <Descriptions.Item label="改派费用">
                  <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                    ¥{(detailsForm.getFieldValue("amount") || 0).toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="货币">
                  {detailsForm.getFieldValue("currency") || "CNY"}
                </Descriptions.Item>

                <Descriptions.Item label="生效日期">
                  {detailsForm.getFieldValue("effectiveDate")
                    ? dayjs(detailsForm.getFieldValue("effectiveDate")).format(
                        "YYYY-MM-DD"
                      )
                    : "-"}
                </Descriptions.Item>

                <Descriptions.Item
                  label="改派原因/备注"
                  contentStyle={{ whiteSpace: "pre-wrap" }}
                >
                  {detailsForm.getFieldValue("description") || "-"}
                </Descriptions.Item>
              </Descriptions>

              {/* 作废相关信息 */}
              {selectedAdjustment.isVoid && (
                <div
                  style={{
                    marginTop: "16px",
                    padding: "12px",
                    border: "1px solid #ffccc7",
                    borderRadius: "4px",
                    backgroundColor: "#fff2f0",
                  }}
                >
                  <Descriptions
                    column={1}
                    bordered={false}
                    size="small"
                    title="作废信息"
                  >
                    <Descriptions.Item
                      label="作废原因"
                      contentStyle={{ whiteSpace: "pre-wrap" }}
                    >
                      {detailsForm.getFieldValue("voidReason") || "-"}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              )}
            </div>
          )}

        {selectedAdjustment &&
          selectedAdjustment.adjustmentType === "DESTRUCTION" && (
            <div>
              <Descriptions
                column={1}
                bordered
                size="small"
                style={{ marginBottom: "16px" }}
              >
                <Descriptions.Item label="销毁费用">
                  <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                    ¥{(detailsForm.getFieldValue("amount") || 0).toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="货币">
                  {detailsForm.getFieldValue("currency") || "CNY"}
                </Descriptions.Item>

                <Descriptions.Item label="生效日期">
                  {detailsForm.getFieldValue("effectiveDate")
                    ? dayjs(detailsForm.getFieldValue("effectiveDate")).format(
                        "YYYY-MM-DD"
                      )
                    : "-"}
                </Descriptions.Item>

                <Descriptions.Item
                  label="销毁原因/备注"
                  contentStyle={{ whiteSpace: "pre-wrap" }}
                >
                  {detailsForm.getFieldValue("description") || "-"}
                </Descriptions.Item>
              </Descriptions>

              {/* 作废相关信息 */}
              {selectedAdjustment.isVoid && (
                <div
                  style={{
                    marginTop: "16px",
                    padding: "12px",
                    border: "1px solid #ffccc7",
                    borderRadius: "4px",
                    backgroundColor: "#fff2f0",
                  }}
                >
                  <Descriptions
                    column={1}
                    bordered={false}
                    size="small"
                    title="作废信息"
                  >
                    <Descriptions.Item
                      label="作废原因"
                      contentStyle={{ whiteSpace: "pre-wrap" }}
                    >
                      {detailsForm.getFieldValue("voidReason") || "-"}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              )}
            </div>
          )}

        {selectedAdjustment &&
          selectedAdjustment.adjustmentType === "RETURN" && (
            <div>
              <Descriptions
                column={1}
                bordered
                size="small"
                style={{ marginBottom: "16px" }}
              >
                <Descriptions.Item label="退回金额">
                  <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                    ¥{(detailsForm.getFieldValue("amount") || 0).toFixed(2)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item label="货币">
                  {detailsForm.getFieldValue("currency") || "CNY"}
                </Descriptions.Item>

                <Descriptions.Item label="生效日期">
                  {detailsForm.getFieldValue("effectiveDate")
                    ? dayjs(detailsForm.getFieldValue("effectiveDate")).format(
                        "YYYY-MM-DD"
                      )
                    : "-"}
                </Descriptions.Item>

                <Descriptions.Item
                  label="退回原因/备注"
                  contentStyle={{ whiteSpace: "pre-wrap" }}
                >
                  {detailsForm.getFieldValue("description") || "-"}
                </Descriptions.Item>
              </Descriptions>

              {/* 作废相关信息 */}
              {selectedAdjustment.isVoid && (
                <div
                  style={{
                    marginTop: "16px",
                    padding: "12px",
                    border: "1px solid #ffccc7",
                    borderRadius: "4px",
                    backgroundColor: "#fff2f0",
                  }}
                >
                  <Descriptions
                    column={1}
                    bordered={false}
                    size="small"
                    title="作废信息"
                  >
                    <Descriptions.Item
                      label="作废原因"
                      contentStyle={{ whiteSpace: "pre-wrap" }}
                    >
                      {detailsForm.getFieldValue("voidReason") || "-"}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              )}
            </div>
          )}

        {selectedAdjustment &&
          selectedAdjustment.adjustmentType === "COMPENSATION" && (
            <div style={{ display: "flex", flexWrap: "wrap", gap: "24px" }}>
              {/* Left side: Options and adjustments */}
              <div style={{ flex: "1", minWidth: "420px" }}>
                <Descriptions
                  column={2}
                  bordered
                  size="small"
                  style={{ marginBottom: "16px" }}
                >
                  <Descriptions.Item label="是否减免运费">
                    {detailsForm.getFieldValue("isFreightDeduction")
                      ? "是"
                      : "否"}
                  </Descriptions.Item>

                  <Descriptions.Item label="是否赔偿货值">
                    {detailsForm.getFieldValue("isValueCompensation")
                      ? "是"
                      : "否"}
                  </Descriptions.Item>

                  <Descriptions.Item label="生效日期">
                    {detailsForm.getFieldValue("effectiveDate")
                      ? dayjs(
                          detailsForm.getFieldValue("effectiveDate")
                        ).format("YYYY-MM-DD")
                      : "-"}
                  </Descriptions.Item>

                  <Descriptions.Item label="货币">
                    {detailsForm.getFieldValue("currency") || "CNY"}
                  </Descriptions.Item>
                </Descriptions>

                {detailsForm.getFieldValue("isFreightDeduction") && (
                  <Descriptions
                    column={2}
                    bordered
                    size="small"
                    style={{ marginBottom: "16px" }}
                  >
                    <Descriptions.Item label="减免运费比例 (%)">
                      {detailsForm.getFieldValue(
                        "freightDeductionPercentage"
                      ) || "0"}
                    </Descriptions.Item>

                    <Descriptions.Item label="减免运费总额">
                      ¥
                      {(
                        detailsForm.getFieldValue(
                          "totalFreightDeductionAmount"
                        ) || 0
                      ).toFixed(2)}
                    </Descriptions.Item>
                  </Descriptions>
                )}

                {detailsForm.getFieldValue("isValueCompensation") && (
                  <Descriptions
                    column={3}
                    bordered
                    size="small"
                    style={{ marginBottom: "16px" }}
                  >
                    <Descriptions.Item label="货值">
                      ¥
                      {(detailsForm.getFieldValue("cargoValue") || 0).toFixed(
                        2
                      )}
                    </Descriptions.Item>

                    <Descriptions.Item label="货值赔偿比例 (%)">
                      {detailsForm.getFieldValue(
                        "valueCompensationPercentage"
                      ) || "0"}
                    </Descriptions.Item>

                    <Descriptions.Item label="货值赔偿总额">
                      ¥
                      {(
                        detailsForm.getFieldValue(
                          "totalValueCompensationAmount"
                        ) || 0
                      ).toFixed(2)}
                    </Descriptions.Item>
                  </Descriptions>
                )}

                <Descriptions
                  column={1}
                  bordered
                  size="small"
                  style={{ marginBottom: "16px" }}
                >
                  <Descriptions.Item label="总赔偿金额">
                    <span style={{ fontSize: "18px", fontWeight: "bold" }}>
                      ¥
                      {(
                        detailsForm.getFieldValue("totalCompensationAmount") ||
                        0
                      ).toFixed(2)}
                    </span>
                  </Descriptions.Item>
                </Descriptions>
              </div>

              {/* Right side: Description and proof */}
              <div style={{ flex: "1", minWidth: "420px" }}>
                <Descriptions
                  column={1}
                  bordered
                  size="small"
                  style={{ marginBottom: "16px" }}
                >
                  <Descriptions.Item
                    label="赔偿说明/备注"
                    contentStyle={{ whiteSpace: "pre-wrap" }}
                  >
                    {detailsForm.getFieldValue("description") || "-"}
                  </Descriptions.Item>
                </Descriptions>

                <Descriptions
                  column={1}
                  bordered
                  size="small"
                  style={{ marginBottom: "16px" }}
                >
                  <Descriptions.Item
                    label="货值证明图片"
                    contentStyle={{ whiteSpace: "pre-wrap" }}
                  >
                    {(() => {
                      const imageUrls = detailsForm.getFieldValue(
                        "proofOfValueImageUrls"
                      );
                      const urlArray = Array.isArray(imageUrls)
                        ? imageUrls
                        : (imageUrls || "")
                            .split("\n")
                            .map((url: string) => url.trim())
                            .filter((url: string) => url);

                      if (urlArray.length === 0) {
                        return <span>-</span>;
                      }

                      return (
                        <div>
                          <div
                            style={{
                              display: "flex",
                              flexWrap: "wrap",
                              gap: "8px",
                              marginBottom: "8px",
                            }}
                          >
                            {urlArray.map((url: string, index: number) => (
                              <div
                                key={index}
                                style={{
                                  width: "60px",
                                  height: "60px",
                                  border: "1px solid #d9d9d9",
                                  borderRadius: "4px",
                                  overflow: "hidden",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                }}
                              >
                                <Image
                                  src={url}
                                  alt={`货值证明图片${index + 1}`}
                                  style={{
                                    width: "100%",
                                    height: "100%",
                                    objectFit: "cover",
                                  }}
                                  preview={{
                                    mask: (
                                      <EyeOutlined style={{ color: "#fff" }} />
                                    ),
                                  }}
                                  fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMCAyNEMzMS42NTY5IDI0IDMzIDI1LjM0MzEgMzMgMjdDMzMgMjguNjU2OSAzMS42NTY5IDMwIDMwIDMwQzI4LjM0MzEgMzAgMjcgMjguNjU2OSAyNyAyN0MyNyAyNS4zNDMxIDI4LjM0MzEgMjQgMzAgMjRaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik0yMSAzNkwzMCAzMEwzOSAzNkgyMVoiIGZpbGw9IiNCRkJGQkYiLz4KPHN2Zz4K"
                                />
                              </div>
                            ))}
                          </div>
                          <div style={{ fontSize: "12px", color: "#666" }}>
                            共 {urlArray.length} 张图片，点击可放大查看
                          </div>
                        </div>
                      );
                    })()}
                  </Descriptions.Item>
                </Descriptions>

                {/* 作废相关信息 */}
                {selectedAdjustment.isVoid && (
                  <div
                    style={{
                      marginTop: "16px",
                      padding: "12px",
                      border: "1px solid #ffccc7",
                      borderRadius: "4px",
                      backgroundColor: "#fff2f0",
                    }}
                  >
                    <Descriptions
                      column={1}
                      bordered={false}
                      size="small"
                      title="作废信息"
                    >
                      <Descriptions.Item
                        label="作废原因"
                        contentStyle={{ whiteSpace: "pre-wrap" }}
                      >
                        {detailsForm.getFieldValue("voidReason") || "-"}
                      </Descriptions.Item>
                    </Descriptions>
                  </div>
                )}
              </div>
            </div>
          )}
      </Modal>

      {/* 改派模态框 (Reassignment Modal) */}
      <Modal
        title="添加改派调整"
        open={reassignmentModalVisible}
        onCancel={handleReassignmentModalCancel}
        onOk={handleReassignmentSubmit}
        confirmLoading={submittingReassignment}
        maskClosable={false}
        destroyOnClose
        width={700}
      >
        <Form
          form={reassignmentForm}
          layout="vertical"
          name="reassignmentForm"
          initialValues={{ currency: "CNY" }}
          style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}
        >
          <Form.Item name="manifestId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="customerAccountId" hidden>
            <Input />
          </Form.Item>

          {/* Left side */}
          <div style={{ flex: "1", minWidth: "320px" }}>
            <Form.Item
              name="reassignmentNumber"
              label="改派单号"
              rules={[{ required: true, message: "请输入改派单号" }]}
            >
              <Input placeholder="请输入改派单号" />
            </Form.Item>

            <div style={{ display: "flex", gap: "16px" }}>
              <Form.Item
                name="amount"
                label="改派费用"
                rules={[
                  {
                    required: true,
                    message: "请输入改派费用",
                  },
                  {
                    validator: (_, value) => {
                      if (value > 0) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error("改派费用必须大于0"));
                    },
                  },
                ]}
                style={{ flex: "1" }}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  prefix="¥"
                  precision={2}
                  min={0.01}
                />
              </Form.Item>

              <Form.Item
                name="currency"
                label="货币"
                rules={[{ required: true, message: "请选择货币" }]}
                style={{ flex: "1" }}
              >
                <Input disabled />
              </Form.Item>
            </div>

            <Form.Item
              name="effectiveDate"
              label="生效日期"
              rules={[{ required: true, message: "请选择生效日期" }]}
            >
              <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
            </Form.Item>
          </div>

          {/* Right side */}
          <div style={{ flex: "1", minWidth: "320px" }}>
            <Form.Item
              name="description"
              label="改派原因/备注"
              style={{ height: "100%" }}
            >
              <Input.TextArea
                rows={3}
                placeholder="请简要说明改派原因或相关情况"
                style={{ height: "100%" }}
              />
            </Form.Item>
          </div>
        </Form>
      </Modal>

      {/* 销毁模态框 (Destruction Modal) */}
      <Modal
        title="添加销毁"
        open={destructionModalVisible}
        onCancel={handleDestructionModalCancel}
        onOk={handleDestructionSubmit}
        confirmLoading={submittingDestruction}
        maskClosable={false}
        destroyOnClose
        width={600}
      >
        <Form form={destructionForm} layout="vertical" name="destructionForm">
          <Form.Item name="manifestId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="customerAccountId" hidden>
            <Input />
          </Form.Item>

          <div style={{ display: "flex", gap: "24px" }}>
            <Form.Item
              name="amount"
              label="销毁费用"
              rules={[{ required: true, message: "请输入销毁费用" }]}
              style={{ flex: "1" }}
            >
              <InputNumber
                style={{ width: "100%" }}
                min={0}
                precision={2}
                prefix="¥"
              />
            </Form.Item>

            <Form.Item
              name="effectiveDate"
              label="生效日期"
              rules={[{ required: true, message: "请选择生效日期" }]}
              style={{ flex: "1" }}
            >
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </div>

          <Form.Item name="description" label="销毁原因/备注">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 退回模态框 (Return Modal) */}
      <Modal
        title="添加退回"
        open={returnModalVisible}
        onCancel={handleReturnModalCancel}
        onOk={handleReturnSubmit}
        confirmLoading={submittingReturn}
        maskClosable={false}
        destroyOnClose
        width={700}
      >
        <Form
          form={returnForm}
          layout="vertical"
          name="returnForm"
          initialValues={{ currency: "CNY" }}
        >
          <Form.Item name="manifestId" hidden>
            <Input />
          </Form.Item>
          <Form.Item name="customerAccountId" hidden>
            <Input />
          </Form.Item>

          <div style={{ display: "flex", gap: "16px", marginBottom: "16px" }}>
            <Form.Item
              name="amount"
              label="退回金额"
              rules={[{ required: true, message: "请输入退回金额" }]}
              style={{ flex: "1" }}
            >
              <InputNumber
                style={{ width: "100%" }}
                min={0.01}
                step={0.01}
                precision={2}
                placeholder="请输入退回金额"
                prefix="¥"
              />
            </Form.Item>

            <Form.Item
              name="currency"
              label="货币"
              initialValue="CNY"
              style={{ flex: "1" }}
            >
              <Input
                style={{ width: "100%" }}
                disabled={true}
                defaultValue="人民币 (CNY)"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="effectiveDate"
            label="生效日期"
            rules={[{ required: true, message: "请选择生效日期" }]}
          >
            <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
          </Form.Item>

          <Form.Item name="description" label="退回原因/备注">
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ManifestDetailDrawer;
