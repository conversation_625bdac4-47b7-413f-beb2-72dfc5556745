import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { fileURLToPath } from "url";

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载env文件中的环境变量
  const env = loadEnv(mode, process.cwd());

  // 开发环境下使用代理，生产环境不需要
  const devProxy = {
    "/api": {
      target: "http://localhost:8080",
      changeOrigin: true,
      secure: false,
      ws: true,
      logLevel: "debug",
      // 后端API路径已包含/api/v1前缀，无需重写
      // rewrite: (path: string) => path.replace(/^\/api/, '')
    },
  };

  return {
    plugins: [react()],
    base: mode === "production" ? "/admin/" : "/",
    // 解析别名
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    // 开发服务器配置
    server: {
      port: 5173,
      open: true,
      proxy: command === "serve" ? devProxy : undefined,
    },
    // 构建选项
    build: {
      // 输出目录
      outDir: "dist",
      // 生成静态资源的存放路径
      assetsDir: "assets",
      // 小于此阈值的导入或引用资源将内联为base64编码
      assetsInlineLimit: 4096,
      // 启用/禁用CSS代码拆分
      cssCodeSplit: true,
      // 构建后是否生成source map文件
      sourcemap: mode !== "production",
    },
  };
});
