import React, { useState, useEffect, useCallback } from "react";
import { Select, Spin, Empty } from "antd";
import debounce from "lodash/debounce";
import { fetchUserOptions, UserOption } from "../../services/userService";

const { Option } = Select;

interface CustomerSelectorProps {
  value?: number;
  onChange?: (value?: number) => void;
  placeholder?: string;
  allowClear?: boolean;
  style?: React.CSSProperties;
  disabled?: boolean;
}

const ITEM_PAGE_SIZE = 20;

const CustomerSelector: React.FC<CustomerSelectorProps> = ({
  value,
  onChange,
  placeholder = "输入客户昵称/用户名搜索",
  allowClear = true,
  style,
  disabled = false,
}) => {
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userLoading, setUserLoading] = useState<boolean>(false);
  const [userPage, setUserPage] = useState<number>(1);
  const [userTotal, setUserTotal] = useState<number>(0);
  const [userSearchKeyword, setUserSearchKeyword] = useState<string>("");

  const loadUserOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setUserLoading(true);
      try {
        const apiResponse = await fetchUserOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;

          if (Array.isArray(list) && typeof total === "number") {
            setUserOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setUserTotal(total);
            setUserPage(page);
          } else {
            console.error(
              "Failed to load user options: Data structure inside ApiResponse.data is invalid.",
              apiResponse
            );
            if (!append) {
              setUserOptions([]);
              setUserTotal(0);
            }
          }
        } else {
          console.error(
            "Failed to load user options:",
            apiResponse.errorMessage || "API call failed or data was null"
          );
          if (!append) {
            setUserOptions([]);
            setUserTotal(0);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching user options (network/exception):",
          error
        );
        if (!append) {
          setUserOptions([]);
          setUserTotal(0);
        }
      } finally {
        setUserLoading(false);
      }
    },
    []
  );

  // 防抖搜索
  const debouncedUserSearch = useCallback(
    debounce((keyword: string) => {
      setUserSearchKeyword(keyword);
      setUserPage(1);
      loadUserOptions(keyword, 1);
    }, 300),
    [loadUserOptions]
  );

  // 处理下拉框滚动加载更多
  const handleUserPopupScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event;
    const element = target as HTMLDivElement;
    if (
      element.scrollTop + element.offsetHeight === element.scrollHeight &&
      userOptions.length < userTotal &&
      !userLoading
    ) {
      const nextPage = userPage + 1;
      loadUserOptions(userSearchKeyword, nextPage, true);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadUserOptions("", 1);
  }, [loadUserOptions]);

  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      showSearch
      filterOption={false}
      onSearch={debouncedUserSearch}
      loading={userLoading}
      onPopupScroll={handleUserPopupScroll}
      style={style}
      disabled={disabled}
      notFoundContent={
        userLoading ? (
          <Spin size="small" />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
        )
      }
    >
      {userOptions.map((user) => (
        <Option key={user.id} value={user.id} title={user.nickname}>
          {user.nickname} ({user.username})
        </Option>
      ))}
    </Select>
  );
};

export default CustomerSelector;
