import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Card,
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  message,
  Tag,
  Typography,
  Tooltip,
  Row,
  Col,
  Statistic,
  InputNumber,
  Breadcrumb,
  Alert,
  Checkbox,
  Divider,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FileTextOutlined,
  DollarOutlined,
  CalendarOutlined,
  PlusOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
  UnorderedListOutlined,
  FileZipOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import type { TablePaginationConfig } from "antd/es/table";
import {
  fetchBillingRecords,
  BillingRecord,
  BillingRecordQueryParams,
  BillStatus,
  BILL_STATUS_OPTIONS,
  CURRENCY_OPTIONS,
  formatBillStatus,
  downloadBillingRecord,
} from "../../services/billingService";
import {
  fetchBillingCycleDetail,
  BillingCycle,
} from "../../services/billingCycleService";
import GenerateBillModal from "./GenerateBillModal";
import BillTaskListModal from "./BillTaskListModal";
import { Link } from "react-router-dom";
import CustomerSelector from "../../components/common/CustomerSelector";
import { apiClient } from "../../services/apiClient";
import { getApiPath } from "../../services/apiPaths";

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 组件状态接口
interface BillManagementState {
  loading: boolean;
  data: BillingRecord[];
  total: number;
  pagination: {
    current: number;
    pageSize: number;
  };
  exportingIds: Set<number>; // 正在导出的账单ID集合
  selectedRowKeys: number[]; // 选中的行keys
  selectedRecords: Map<number, BillingRecord>; // 选中的记录Map，用于跨页保持
  batchExporting: boolean; // 批量导出中状态
}

// 批量导出请求接口
interface BatchExportRequest {
  billingRecordIds: number[];
}

const BillManagementPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const params = useParams();

  // 获取账期批次ID
  const billingCycleId = params.cycleId ? parseInt(params.cycleId, 10) : null;

  // 组件状态
  const [state, setState] = useState<BillManagementState>({
    loading: false,
    data: [],
    total: 0,
    pagination: {
      current: 1,
      pageSize: 10,
    },
    exportingIds: new Set(),
    selectedRowKeys: [],
    selectedRecords: new Map(),
    batchExporting: false,
  });

  // 账期批次信息状态
  const [billingCycle, setBillingCycle] = useState<BillingCycle | null>(null);

  // 统计数据状态
  const [statistics, setStatistics] = useState({
    totalBills: 0,
    unpaidAmount: 0,
    paidAmount: 0,
    overdueCount: 0,
  });

  // 生成账单弹窗状态
  const [generateModalVisible, setGenerateModalVisible] = useState(false);

  // 任务列表弹窗状态
  const [taskListModalVisible, setTaskListModalVisible] = useState(false);

  /**
   * 加载账期批次详情
   */
  const loadBillingCycleDetail = async () => {
    if (!billingCycleId) return;

    try {
      const cycleDetail = await fetchBillingCycleDetail(billingCycleId);
      setBillingCycle(cycleDetail);
    } catch (error) {
      console.error("加载账期批次详情失败:", error);
      message.error("加载账期批次详情失败，请重试");
    }
  };

  /**
   * 加载账单数据
   */
  const loadBillingRecords = async (params?: BillingRecordQueryParams) => {
    setState((prev) => ({ ...prev, loading: true }));

    try {
      const queryParams: BillingRecordQueryParams = {
        page: state.pagination.current,
        pageSize: state.pagination.pageSize,
        billingCycleId: billingCycleId || undefined, // 添加账期批次ID过滤
        ...params,
      };

      const result = await fetchBillingRecords(queryParams);

      setState((prev) => ({
        ...prev,
        data: result.list,
        total: result.total,
        loading: false,
      }));

      // 计算统计数据
      calculateStatistics(result.list);
    } catch (error) {
      console.error("加载账单数据失败:", error);
      message.error("加载账单数据失败，请重试");
      setState((prev) => ({ ...prev, loading: false }));
    }
  };

  /**
   * 计算统计数据
   */
  const calculateStatistics = (records: BillingRecord[]) => {
    const stats = {
      totalBills: records.length,
      unpaidAmount: 0,
      paidAmount: 0,
      overdueCount: 0,
    };

    records.forEach((record) => {
      if (
        record.status === BillStatus.UNPAID ||
        record.status === BillStatus.PARTIAL_PAID
      ) {
        stats.unpaidAmount += record.balanceDue;
      }
      if (record.status === BillStatus.PAID) {
        stats.paidAmount += record.amountPaid;
      }
      if (record.status === BillStatus.OVERDUE) {
        stats.overdueCount += 1;
      }
    });

    setStatistics(stats);
  };

  /**
   * 处理搜索
   */
  const handleSearch = () => {
    const formValues = form.getFieldsValue();

    // 处理日期范围
    const billDateRange = formValues.billDateRange;
    const billingPeriodRange = formValues.billingPeriodRange;

    // 构建搜索参数
    const searchParams: BillingRecordQueryParams = {
      billNumber: formValues.billNumber,
      customerAccountId: formValues.customerAccountId,
      status: formValues.status,
      currency: formValues.currency,
      minAmount: formValues.minAmount,
      maxAmount: formValues.maxAmount,
      billDateStart: billDateRange?.[0]?.format("YYYY-MM-DD"),
      billDateEnd: billDateRange?.[1]?.format("YYYY-MM-DD"),
      billingPeriodStart: billingPeriodRange?.[0]?.format("YYYY-MM-DD"),
      billingPeriodEnd: billingPeriodRange?.[1]?.format("YYYY-MM-DD"),
    };

    // 重置分页到第一页
    setState((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, current: 1 },
    }));

    loadBillingRecords(searchParams);
  };

  /**
   * 重置搜索条件
   */
  const handleReset = () => {
    form.resetFields();
    setState((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, current: 1 },
    }));
    loadBillingRecords();
  };

  /**
   * 处理表格分页变化
   */
  const handleTableChange = (pagination: TablePaginationConfig) => {
    const newPagination = {
      current: pagination.current || 1,
      pageSize: pagination.pageSize || 10,
    };

    setState((prev) => ({
      ...prev,
      pagination: newPagination,
    }));

    // 获取当前搜索条件并重新查询
    const formValues = form.getFieldsValue();
    const billDateRange = formValues.billDateRange;
    const billingPeriodRange = formValues.billingPeriodRange;

    // 构建搜索参数
    const searchParams: BillingRecordQueryParams = {
      billNumber: formValues.billNumber,
      customerAccountId: formValues.customerAccountId,
      status: formValues.status,
      currency: formValues.currency,
      minAmount: formValues.minAmount,
      maxAmount: formValues.maxAmount,
      page: newPagination.current,
      pageSize: newPagination.pageSize,
      billDateStart: billDateRange?.[0]?.format("YYYY-MM-DD"),
      billDateEnd: billDateRange?.[1]?.format("YYYY-MM-DD"),
      billingPeriodStart: billingPeriodRange?.[0]?.format("YYYY-MM-DD"),
      billingPeriodEnd: billingPeriodRange?.[1]?.format("YYYY-MM-DD"),
    };

    loadBillingRecords(searchParams);
  };

  /**
   * 处理表格行选择变化
   */
  const handleSelectionChange = (
    selectedRowKeys: React.Key[],
    selectedRows: BillingRecord[]
  ) => {
    const newSelectedRowKeys = selectedRowKeys as number[];

    setState((prev) => {
      const newSelectedRecords = new Map(prev.selectedRecords);

      // 移除当前页面取消选择的记录
      const currentPageIds = prev.data.map((record) => record.id);
      currentPageIds.forEach((id) => {
        if (!newSelectedRowKeys.includes(id)) {
          newSelectedRecords.delete(id);
        }
      });

      // 添加当前页面新选择的记录
      selectedRows.forEach((record) => {
        newSelectedRecords.set(record.id, record);
      });

      return {
        ...prev,
        selectedRowKeys: Array.from(newSelectedRecords.keys()),
        selectedRecords: newSelectedRecords,
      };
    });
  };

  /**
   * 全选当前页面
   */
  const handleSelectAll = () => {
    const currentPageIds = state.data.map((record) => record.id);
    const isAllSelected = currentPageIds.every((id) =>
      state.selectedRowKeys.includes(id)
    );

    setState((prev) => {
      const newSelectedRecords = new Map(prev.selectedRecords);

      if (isAllSelected) {
        // 取消选择当前页面所有记录
        currentPageIds.forEach((id) => {
          newSelectedRecords.delete(id);
        });
      } else {
        // 选择当前页面所有记录
        state.data.forEach((record) => {
          newSelectedRecords.set(record.id, record);
        });
      }

      return {
        ...prev,
        selectedRowKeys: Array.from(newSelectedRecords.keys()),
        selectedRecords: newSelectedRecords,
      };
    });
  };

  /**
   * 清空所有选择
   */
  const handleClearSelection = () => {
    setState((prev) => ({
      ...prev,
      selectedRowKeys: [],
      selectedRecords: new Map(),
    }));
  };

  /**
   * 批量导出账单
   */
  const handleBatchExport = async () => {
    if (state.selectedRowKeys.length === 0) {
      message.warning("请先选择要导出的账单记录");
      return;
    }

    if (state.selectedRowKeys.length > 100) {
      message.error("批量导出最多支持100个账单记录，请重新选择");
      return;
    }

    try {
      setState((prev) => ({ ...prev, batchExporting: true }));

      const requestData: BatchExportRequest = {
        billingRecordIds: state.selectedRowKeys,
      };

      const response = await apiClient.post(
        getApiPath("/finance/billing-record/batch-export"),
        requestData,
        {
          responseType: "blob",
        }
      );

      // 创建下载链接
      const blob = new Blob([response.data], { type: "application/zip" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;

      // 从响应头获取文件名
      const contentDisposition = response.headers["content-disposition"];
      let fileName = `批量账单导出_${new Date().getTime()}.zip`;

      if (contentDisposition) {
        const fileNameMatch = contentDisposition.match(/filename=(.+)/);
        if (fileNameMatch) {
          fileName = decodeURIComponent(fileNameMatch[1]);
        }
      }

      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      message.success(`成功导出 ${state.selectedRowKeys.length} 个账单记录`);

      // 清空选择状态
      handleClearSelection();
    } catch (error) {
      console.error("批量导出失败:", error);
      message.error("批量导出失败，请重试");
    } finally {
      setState((prev) => ({ ...prev, batchExporting: false }));
    }
  };

  /**
   * 渲染账单状态标签
   */
  const renderStatusTag = (status: BillStatus) => {
    const statusConfig = {
      [BillStatus.UNPAID]: { color: "orange", icon: <DollarOutlined /> },
      [BillStatus.PAID]: { color: "green", icon: <DollarOutlined /> },
      [BillStatus.PARTIAL_PAID]: { color: "blue", icon: <DollarOutlined /> },
      [BillStatus.OVERDUE]: { color: "red", icon: <CalendarOutlined /> },
      [BillStatus.CANCELLED]: { color: "default", icon: <FileTextOutlined /> },
      [BillStatus.ERROR]: { color: "red", icon: <FileTextOutlined /> },
    };

    const config = statusConfig[status];
    return (
      <Tag color={config.color} icon={config.icon}>
        {formatBillStatus(status)}
      </Tag>
    );
  };

  /**
   * 表格行选择配置
   */
  const rowSelection = {
    selectedRowKeys: state.selectedRowKeys.filter((key) =>
      state.data.some((record) => record.id === key)
    ),
    onChange: handleSelectionChange,
    preserveSelectedRowKeys: true,
    getCheckboxProps: (record: BillingRecord) => ({
      name: record.billNumber,
    }),
  };

  /**
   * 表格列定义
   */
  const columns: ColumnsType<BillingRecord> = [
    {
      title: "账单编号",
      dataIndex: "billNumber",
      key: "billNumber",
      width: 180,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: "monospace" }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "客户信息",
      key: "customer",
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.customerNickname}</div>
          <div style={{ fontSize: "12px", color: "#999" }}>
            ID: {record.customerAccountId}
          </div>
        </div>
      ),
    },
    {
      title: "账单日期",
      dataIndex: "billDate",
      key: "billDate",
      width: 120,
      sorter: true,
    },
    {
      title: "账期",
      key: "billingPeriod",
      width: 180,
      render: (_, record) => (
        <div>
          {record.billingPeriodStart} ~ {record.billingPeriodEnd}
        </div>
      ),
    },
    {
      title: "金额信息",
      key: "amount",
      width: 150,
      render: (_, record) => (
        <div>
          <div>
            总额:{" "}
            <strong>
              {record.totalAmount} {record.currency}
            </strong>
          </div>
          <div style={{ fontSize: "12px", color: "#999" }}>
            已付: {record.amountPaid} | 余额: {record.balanceDue}
          </div>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: BillStatus, record: BillingRecord) => (
        <div>
          {renderStatusTag(status)}
          {status === BillStatus.ERROR && record.notes && (
            <div
              style={{ fontSize: "12px", color: "#ff4d4f", marginTop: "4px" }}
            >
              {record.notes}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "付款截止日期",
      dataIndex: "dueDate",
      key: "dueDate",
      width: 120,
      render: (dueDate: string) => dueDate || "-",
    },
    {
      title: "生成信息",
      key: "generator",
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.generatedByNickname || "系统"}</div>
          <div style={{ fontSize: "12px", color: "#999" }}>
            {record.createTime}
          </div>
        </div>
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 160,
      fixed: "right",
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => handleExportBilling(record)}
            loading={state.exportingIds.has(record.id)}
            title="导出账单"
          >
            导出
          </Button>
        </Space>
      ),
    },
  ];

  /**
   * 查看账单详情
   */
  const handleViewDetail = (record: BillingRecord) => {
    if (billingCycleId) {
      // 如果在特定账期批次下，使用完整路径
      navigate(`/billing-finance/cycles/${billingCycleId}/bills/${record.id}`);
    } else {
      // 如果是独立的账单管理页面，使用简化路径
      navigate(`/billing-finance/bills/${record.id}`);
    }
  };

  /**
   * 导出账单Excel
   */
  const handleExportBilling = async (record: BillingRecord) => {
    // 添加到导出中的ID集合
    setState((prev) => ({
      ...prev,
      exportingIds: new Set([...prev.exportingIds, record.id]),
    }));

    try {
      await downloadBillingRecord(record.id);
      message.success("账单导出成功");
    } catch (error) {
      console.error("导出账单失败:", error);
      message.error(
        error instanceof Error ? error.message : "导出账单失败，请重试"
      );
    } finally {
      // 从导出中的ID集合移除
      setState((prev) => {
        const newExportingIds = new Set(prev.exportingIds);
        newExportingIds.delete(record.id);
        return {
          ...prev,
          exportingIds: newExportingIds,
        };
      });
    }
  };

  /**
   * 打开生成账单弹窗
   */
  const handleOpenGenerateModal = () => {
    setGenerateModalVisible(true);
  };

  /**
   * 关闭生成账单弹窗
   */
  const handleCloseGenerateModal = () => {
    setGenerateModalVisible(false);
  };

  /**
   * 生成账单成功回调
   */
  const handleGenerateSuccess = () => {
    // 刷新账单列表
    loadBillingRecords();

    // 如果有账单批次ID，重新加载账单批次数据
    if (billingCycleId) {
      loadBillingCycleDetail();
    }

    message.success("账单生成成功，已刷新列表");
  };

  /**
   * 打开任务列表弹窗
   */
  const handleOpenTaskListModal = () => {
    setTaskListModalVisible(true);
  };

  /**
   * 关闭任务列表弹窗
   */
  const handleCloseTaskListModal = () => {
    setTaskListModalVisible(false);
  };

  // 初始化加载数据
  useEffect(() => {
    loadBillingCycleDetail();
    loadBillingRecords();
  }, [billingCycleId]);

  /**
   * 返回账期批次管理页面
   */
  const handleBackToCycleManagement = () => {
    navigate("/billing-finance/cycles");
  };

  // 计算当前页面的选择状态
  const currentPageIds = state.data.map((record) => record.id);
  const currentPageSelectedCount = currentPageIds.filter((id) =>
    state.selectedRowKeys.includes(id)
  ).length;
  const isCurrentPageAllSelected =
    currentPageIds.length > 0 &&
    currentPageIds.every((id) => state.selectedRowKeys.includes(id));
  const isCurrentPagePartialSelected =
    currentPageSelectedCount > 0 && !isCurrentPageAllSelected;

  return (
    <div style={{ padding: "24px" }}>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: "16px" }}>
        <Breadcrumb.Item>
          <Link to="/">首页</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Link to="/billing-finance">账单管理</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>账单详情</Breadcrumb.Item>
      </Breadcrumb>

      {/* 账期信息展示 */}
      {billingCycle && (
        <Alert
          message={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <div>
                <strong>{billingCycle.cycleName}</strong>
                <span style={{ marginLeft: "16px", color: "#666" }}>
                  {billingCycle.cycleYear}年{billingCycle.cycleMonth}月账期
                </span>
                {billingCycle.notes && (
                  <span style={{ marginLeft: "16px", color: "#999" }}>
                    备注: {billingCycle.notes}
                  </span>
                )}
              </div>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBackToCycleManagement}
              >
                返回账期管理
              </Button>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: "24px" }}
        />
      )}

      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px",
        }}
      >
        <Title level={2} style={{ margin: 0 }}>
          {billingCycle ? `${billingCycle.cycleName} - 账单管理` : "账单管理"}
        </Title>
        <Space>
          {billingCycleId && (
            <Button
              icon={<UnorderedListOutlined />}
              onClick={handleOpenTaskListModal}
            >
              生成任务
            </Button>
          )}
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleOpenGenerateModal}
          >
            生成账单
          </Button>
        </Space>
      </div>

      {/* 统计卡片 - 显示账期批次详情信息 */}
      {billingCycle ? (
        <Row gutter={16} style={{ marginBottom: "24px" }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="客户总数"
                value={billingCycle.totalCustomersBilled || 0}
                prefix={<FileTextOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="账单总数"
                value={billingCycle.totalBillsGenerated || 0}
                prefix={<FileTextOutlined />}
                suffix="张"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="账期总金额"
                value={billingCycle.totalBilledAmount || 0}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="元"
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已付金额"
                value={billingCycle.totalAmountPaidInCycle || 0}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="元"
                valueStyle={{ color: "#3f8600" }}
              />
            </Card>
          </Col>
        </Row>
      ) : (
        <Row gutter={16} style={{ marginBottom: "24px" }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="账单总数"
                value={statistics.totalBills}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="未付金额"
                value={statistics.unpaidAmount}
                precision={2}
                prefix={<DollarOutlined />}
                valueStyle={{ color: "#cf1322" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已付金额"
                value={statistics.paidAmount}
                precision={2}
                prefix={<DollarOutlined />}
                valueStyle={{ color: "#3f8600" }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="逾期账单"
                value={statistics.overdueCount}
                prefix={<CalendarOutlined />}
                valueStyle={{ color: "#cf1322" }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索表单 */}
      <Card style={{ marginBottom: "24px" }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: "16px" }}
        >
          <Row gutter={[16, 16]} style={{ width: "100%" }}>
            <Col span={6}>
              <Form.Item name="billNumber" label="账单编号">
                <Input placeholder="请输入账单编号" allowClear />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="customerAccountId" label="客户筛选">
                <CustomerSelector
                  placeholder="输入客户昵称/用户名搜索"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="status" label="账单状态">
                <Select placeholder="请选择账单状态" allowClear>
                  {BILL_STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="currency" label="货币">
                <Select placeholder="请选择货币" allowClear>
                  {CURRENCY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="billDateRange" label="账单日期">
                <RangePicker
                  style={{ width: "100%" }}
                  placeholder={["开始日期", "结束日期"]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="billingPeriodRange" label="账期范围">
                <RangePicker
                  style={{ width: "100%" }}
                  placeholder={["账期开始", "账期结束"]}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="金额范围">
                <Input.Group compact>
                  <Form.Item name="minAmount" noStyle>
                    <InputNumber
                      placeholder="最小金额"
                      style={{ width: "50%" }}
                      min={0}
                    />
                  </Form.Item>
                  <Form.Item name="maxAmount" noStyle>
                    <InputNumber
                      placeholder="最大金额"
                      style={{ width: "50%" }}
                      min={0}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                    loading={state.loading}
                  >
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 选择操作栏 */}
      <Card style={{ marginBottom: "16px" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Space size="middle">
            <Checkbox
              indeterminate={isCurrentPagePartialSelected}
              checked={isCurrentPageAllSelected}
              onChange={handleSelectAll}
            >
              当前页面全选
            </Checkbox>

            <Divider type="vertical" />

            <span style={{ color: "#666", fontSize: "14px" }}>
              已选择{" "}
              <strong style={{ color: "#1890ff" }}>
                {state.selectedRowKeys.length}
              </strong>{" "}
              项
            </span>

            {state.selectedRowKeys.length > 0 && (
              <Button
                type="link"
                size="small"
                onClick={handleClearSelection}
                style={{ padding: 0 }}
              >
                清空选择
              </Button>
            )}
          </Space>

          <Space>
            <Button
              type="primary"
              icon={<FileZipOutlined />}
              onClick={handleBatchExport}
              loading={state.batchExporting}
              disabled={state.selectedRowKeys.length === 0}
            >
              批量导出 ({state.selectedRowKeys.length})
            </Button>
          </Space>
        </div>
      </Card>

      {/* 账单列表 */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={state.data}
          rowKey="id"
          loading={state.loading}
          pagination={{
            current: state.pagination.current,
            pageSize: state.pagination.pageSize,
            total: state.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示 ${range[0]}-${range[1]} 条记录，共 ${total} 条`,
            pageSizeOptions: ["10", "20", "50", "100"],
          }}
          onChange={handleTableChange}
          scroll={{ x: 1400 }}
          size="middle"
        />
      </Card>

      {/* 生成账单弹窗 */}
      <GenerateBillModal
        visible={generateModalVisible}
        onClose={handleCloseGenerateModal}
        onSuccess={handleGenerateSuccess}
        billingCycleId={billingCycleId || undefined}
        billingCycle={billingCycle || undefined}
      />

      {/* 任务列表弹窗 */}
      <BillTaskListModal
        visible={taskListModalVisible}
        onClose={handleCloseTaskListModal}
        billingCycleId={billingCycleId || undefined}
        cycleName={billingCycle?.cycleName}
      />
    </div>
  );
};

export default BillManagementPage;
