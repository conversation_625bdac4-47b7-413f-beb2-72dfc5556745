# 账单管理功能说明

## 功能概述

账单管理页面是财务管理模块下的一个新增功能，用于查询、筛选和管理客户账单记录。

## 功能特性

### 1. 账单查询与筛选

- **账单编号搜索**: 支持模糊查询账单编号
- **客户筛选**: 根据客户 ID 筛选账单
- **状态筛选**: 支持按账单状态筛选（未付款、已付款、部分付款、逾期、已取消）
- **货币筛选**: 支持按货币类型筛选
- **日期范围**: 支持按账单日期和账期范围筛选
- **金额范围**: 支持按最小和最大金额筛选

### 2. 数据展示

- **统计卡片**: 显示账单总数、未付金额、已付金额、逾期账单数量
- **表格展示**: 分页显示账单记录，包含详细信息
- **状态标签**: 用不同颜色和图标显示账单状态
- **响应式设计**: 支持不同屏幕尺寸

### 3. 操作功能

- **查看详情**: 点击详情按钮查看账单详细信息（待实现）
- **分页导航**: 支持页码跳转和每页数量调整
- **数据刷新**: 支持重置搜索条件和重新加载数据

## 技术实现

### 文件结构

```
src/
├── services/
│   └── billingService.ts          # 账单相关API服务
├── pages/
│   └── BillingFinance/
│       └── BillManagementPage.tsx # 账单管理页面组件
├── layouts/
│   └── MainLayout.tsx             # 更新菜单配置
└── App.tsx                        # 更新路由配置
```

### API 接口

- **接口路径**: `/api/v1/finance/billing/records`
- **请求方法**: GET
- **支持参数**: 分页、筛选、排序等多种查询参数

### 数据类型

```typescript
// 账单状态枚举
enum BillStatus {
  UNPAID = "UNPAID", // 未付款
  PAID = "PAID", // 已付款
  PARTIAL_PAID = "PARTIAL_PAID", // 部分付款
  OVERDUE = "OVERDUE", // 逾期
  CANCELLED = "CANCELLED", // 已取消
}
```

## 访问路径

- **菜单路径**: 财务管理 → 账单管理
- **URL 路径**: `/#/billing/bills`

## 使用说明

1. **进入页面**: 从左侧菜单点击"财务管理" → "账单管理"
2. **查看统计**: 页面顶部显示账单统计信息
3. **筛选数据**: 使用搜索表单设置筛选条件
4. **查看结果**: 在表格中查看筛选后的账单记录
5. **分页浏览**: 使用表格底部的分页控件浏览更多数据

## 后续优化

1. **账单详情**: 实现账单详情查看功能
2. **导出功能**: 支持导出账单数据为 Excel 或 PDF
3. **批量操作**: 支持批量标记、删除等操作
4. **高级筛选**: 增加更多筛选条件和保存筛选方案
5. **实时更新**: 支持数据实时刷新和推送

## 注意事项

1. 确保后端 API 接口已正确实现
2. 需要配置正确的 API 基础路径
3. 用户需要有相应的权限才能访问此功能
4. 建议在生产环境中对大量数据进行性能优化
