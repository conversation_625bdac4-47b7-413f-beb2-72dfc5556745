# 模板类型回显错误修复总结

## 问题分析

原问题出现在 `CreateTemplateModal` 组件中，具体表现为：
1. 第一次点击某个货物类型的"新增"按钮时，弹窗中的模板类型显示的是上一次操作的值
2. 需要关闭弹窗后再次点击才能正确回显

**根本原因：**
- `CreateTemplateModal` 使用 `initialValues` 设置表单初始值，但 `initialValues` 只在组件首次渲染时生效
- 虽然使用了 `destroyOnClose` 属性，但在某些情况下，状态更新的时机可能导致使用了旧的 `selectedTypeId` 值
- React 状态更新是异步的，可能存在时序问题

## 修复方案

### 1. 添加 useEffect 监听状态变化

在 `CreateTemplateModal` 组件中添加 `useEffect`，监听 `visible` 和 `selectedTypeId` 的变化：

```typescript
// 当弹窗打开时，根据模式设置表单值
useEffect(() => {
  if (visible) {
    if (isEditing && editingTemplate) {
      // 编辑模式：设置编辑模板的所有字段
      form.setFieldsValue({
        name: editingTemplate.name,
        type: editingTemplate.type,
        // ... 其他字段
      });
    } else if (selectedTypeId) {
      // 新增模式：设置默认值和选中的模板类型
      form.setFieldsValue({
        type: selectedTypeId,
        firstWeightRange: 0.5,
        continuedWeightInterval: 0.5,
        bulkCoefficient: 6000,
        threeSidesStart: 100,
      });
    }
  }
}, [visible, selectedTypeId, isEditing, editingTemplate, form]);
```

### 2. 优化状态更新时机

在 `UserTemplateConfigPage` 中优化 `handleCreateTemplate` 函数：

```typescript
// 处理新增模板
const handleCreateTemplate = (shipmentType: ShipmentType) => {
  const templateType = getTemplateTypeFromShipmentType(shipmentType);
  // 先设置模板类型，然后在下一个事件循环中打开弹窗，确保状态更新完成
  setSelectedTypeForCreate(templateType);
  // 使用 setTimeout 确保状态更新完成后再打开弹窗
  setTimeout(() => {
    setCreateModalVisible(true);
  }, 0);
};
```

### 3. 调整 initialValues 逻辑

修改 `getInitialValues` 函数，对于新增模式不再设置 `type` 字段，而是通过 `useEffect` 动态设置：

```typescript
// 设置表单初始值
const getInitialValues = () => {
  if (isEditing && editingTemplate) {
    return {
      // 编辑模式的完整字段
    };
  }
  // 对于新增模式，返回基础默认值，具体的 type 值会在 useEffect 中设置
  return {
    firstWeightRange: 0.5,
    continuedWeightInterval: 0.5,
    bulkCoefficient: 6000,
    threeSidesStart: 100,
  };
};
```

## 修复效果

修复后的行为：
1. 每次点击任何货物类型的"新增"按钮时，弹窗都会立即正确显示对应的模板类型
2. 不再需要二次点击来获得正确的回显
3. 编辑模式和新增模式都能正确工作
4. 状态更新更加可靠和一致

## 技术要点

1. **useEffect 依赖管理**：正确设置依赖数组，确保在关键状态变化时触发更新
2. **异步状态更新**：使用 `setTimeout` 确保状态更新完成后再执行后续操作
3. **表单值动态设置**：使用 `form.setFieldsValue` 动态设置表单值，而不仅仅依赖 `initialValues`
4. **模式区分**：正确区分编辑模式和新增模式，应用不同的处理逻辑

这个修复方案解决了 React 状态更新时机问题，确保了用户界面的一致性和可靠性。
