import React, { useState, useEffect } from "react";
import {
  Card,
  Select,
  Table,
  Typography,
  Space,
  Button,
  message,
  Tooltip,
  Spin,
  Modal,
} from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  getActiveShipmentTypes,
  getShippingFeeTemplatesByType,
  deleteShippingFeeTemplate,
  ShipmentType,
  ShippingFeeTemplate,
} from "../../services/shippingFeeService";
import styles from "./ShippingFeeTemplatesPage.module.css";
import CreateTemplateModal from "../../components/ShippingFeeTemplate/CreateTemplateModal";

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 运费模板管理页面
 */
const ShippingFeeTemplatesPage: React.FC = () => {
  // 状态管理
  const [shipmentTypes, setShipmentTypes] = useState<ShipmentType[]>([]);
  const [selectedTypeId, setSelectedTypeId] = useState<number | undefined>(
    undefined
  );
  const [templates, setTemplates] = useState<ShippingFeeTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [typesLoading, setTypesLoading] = useState(true);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<
    ShippingFeeTemplate | undefined
  >(undefined);
  const [isDeleteConfirmVisible, setIsDeleteConfirmVisible] = useState(false);
  const [deletingTemplate, setDeletingTemplate] =
    useState<ShippingFeeTemplate | null>(null);

  // 获取货物类型列表
  const fetchShipmentTypes = async () => {
    try {
      setTypesLoading(true);
      const types = await getActiveShipmentTypes();
      setShipmentTypes(types);

      // 如果有可用的类型，默认选择第一个
      if (types.length > 0) {
        setSelectedTypeId(types[0].id);
      }
    } catch (error) {
      console.error("获取货物类型失败:", error);
      message.error("获取货物类型失败，请刷新页面重试");
    } finally {
      setTypesLoading(false);
    }
  };

  // 根据选中类型获取运费模板
  const fetchTemplatesByType = async (typeId: number) => {
    try {
      setLoading(true);
      const response = await getShippingFeeTemplatesByType(typeId);
      setTemplates(response.templates);
    } catch (error) {
      console.error("获取运费模板失败:", error);
      message.error("获取运费模板失败，请重试");
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理类型选择变化
  const handleTypeChange = (typeId: number) => {
    setSelectedTypeId(typeId);
  };

  // 初始化数据
  useEffect(() => {
    fetchShipmentTypes();
  }, []);

  // 当选中类型变化时，获取对应的模板列表
  useEffect(() => {
    if (selectedTypeId) {
      fetchTemplatesByType(selectedTypeId);
    }
  }, [selectedTypeId]);

  // 表格列定义
  const columns: ColumnsType<ShippingFeeTemplate> = [
    {
      title: "模板名称",
      dataIndex: "name",
      key: "name",
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <Text strong>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: "首重价格",
      dataIndex: "firstWeightPrice",
      key: "firstWeightPrice",
      width: 120,
      render: (price: number) => (
        <Text type="success">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: "首重范围",
      dataIndex: "firstWeightRange",
      key: "firstWeightRange",
      width: 120,
      render: (range: number) => `${range}kg`,
    },
    {
      title: "续重价格",
      dataIndex: "continuedWeightPrice",
      key: "continuedWeightPrice",
      width: 120,
      render: (price: number) => (
        <Text type="warning">¥{price.toFixed(2)}</Text>
      ),
    },
    {
      title: "续重间隔",
      dataIndex: "continuedWeightInterval",
      key: "continuedWeightInterval",
      width: 120,
      render: (interval: number) => `${interval}kg`,
    },
    {
      title: "轻抛系数",
      dataIndex: "bulkCoefficient",
      key: "bulkCoefficient",
      width: 120,
      render: (coefficient: number) => coefficient.toLocaleString(),
    },
    {
      title: "三边和阈值",
      dataIndex: "threeSidesStart",
      key: "threeSidesStart",
      width: 120,
      render: (limit: number) => `${limit}cm`,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 180,
      render: (time: string) => <Text type="secondary">{time}</Text>,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑模板">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleEditTemplate(record);
              }}
            />
          </Tooltip>
          <Tooltip title="删除模板">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteTemplate(record);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 处理编辑模板
  const handleEditTemplate = (template: ShippingFeeTemplate) => {
    setEditingTemplate(template);
    setCreateModalVisible(true);
  };

  // 点击删除按钮，显示确认弹窗
  const handleDeleteTemplate = (template: ShippingFeeTemplate) => {
    setDeletingTemplate(template);
    setIsDeleteConfirmVisible(true);
  };

  // 确认删除模板
  const confirmDelete = async () => {
    if (!deletingTemplate) return;

    try {
      await deleteShippingFeeTemplate(deletingTemplate.id);
      message.success(`成功删除运费模板 "${deletingTemplate.name}"`);
      // 重新加载模板列表
      if (selectedTypeId) {
        fetchTemplatesByType(selectedTypeId);
      }
    } catch (error) {
      // 错误消息由 service 统一处理
      console.error("删除运费模板失败:", error);
    } finally {
      // 关闭弹窗并重置状态
      setIsDeleteConfirmVisible(false);
      setDeletingTemplate(null);
    }
  };

  // 处理新增模板
  const handleAddTemplate = () => {
    if (!selectedTypeId) {
      message.warning("请先选择货物类型");
      return;
    }
    setCreateModalVisible(true);
  };

  // 处理取消创建模板
  const handleCancelCreate = () => {
    setCreateModalVisible(false);
    setEditingTemplate(undefined);
  };

  // 处理创建/编辑模板成功
  const handleCreateSuccess = (template: ShippingFeeTemplate) => {
    setCreateModalVisible(false);
    setEditingTemplate(undefined);
    message.success(
      `运费模板 "${template.name}" ${editingTemplate ? "更新" : "创建"}成功`
    );
    // 重新加载模板列表
    if (selectedTypeId) {
      fetchTemplatesByType(selectedTypeId);
    }
  };

  return (
    <>
      <Title level={4}>运费模板管理</Title>

      <Card>
        <div className={styles.toolbar}>
          <Space>
            <Text>货物类型:</Text>
            <Spin spinning={typesLoading}>
              <Select
                style={{ width: 200 }}
                placeholder="请选择货物类型"
                value={selectedTypeId}
                onChange={handleTypeChange}
                disabled={typesLoading}
              >
                {shipmentTypes.map((type) => (
                  <Option key={type.id} value={type.id}>
                    {type.name}
                  </Option>
                ))}
              </Select>
            </Spin>
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddTemplate}
            disabled={!selectedTypeId}
          >
            新增模板
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`,
          }}
          scroll={{ x: 1300 }}
          className={styles.templateTable}
        />
      </Card>

      <CreateTemplateModal
        visible={createModalVisible}
        selectedTypeId={selectedTypeId}
        onCancel={handleCancelCreate}
        onSuccess={handleCreateSuccess}
        editingTemplate={editingTemplate}
      />

      <Modal
        title={
          <Space>
            <DeleteOutlined style={{ color: "red" }} />
            <Text strong>确认删除</Text>
          </Space>
        }
        open={isDeleteConfirmVisible}
        onOk={confirmDelete}
        onCancel={() => setIsDeleteConfirmVisible(false)}
        okText="确认删除"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <p>
          您确定要删除运费模板 <strong>"{deletingTemplate?.name}"</strong> 吗？
        </p>
        <p>此操作不可恢复，请谨慎操作。</p>
      </Modal>
    </>
  );
};

export default ShippingFeeTemplatesPage;
